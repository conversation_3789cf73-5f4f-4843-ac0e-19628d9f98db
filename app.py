"""
تطبيق Ta9affi الجديد
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_login import LoginManager, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
import os
import pandas as pd
from datetime import datetime, timezone

from models_new import db, User, Role, EducationalLevel, Subject, Domain, KnowledgeMaterial, Competency, Schedule, ProgressEntry, LevelDatabase, LevelDataEntry, AdminInspectorNotification, InspectorTeacherNotification, GeneralNotification, GeneralNotificationRead, inspector_teacher

# تهيئة تطبيق Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///ta9affi_new.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['NOTIFICATIONS_PER_PAGE'] = 10  # عدد الإشعارات في الصفحة الواحدة

# تهيئة قاعدة البيانات
db.init_app(app)

# تهيئة مدير تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# إضافة فلتر nl2br لتنسيق النص في الرسائل
@app.template_filter('nl2br')
def nl2br(value):
    if value:
        return value.replace('\n', '<br>')
    return ''

# تحميل المستخدم
@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# الصفحة الرئيسية
@app.route('/')
def index():
    return render_template('index.html')

# تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if user and check_password_hash(user.password, password):
            # التحقق من حالة الحساب
            if not user.is_active:
                flash('هذا الحساب معطل. يرجى التواصل مع الإدارة.', 'danger')
                return render_template('login.html')

            login_user(user)
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger')

    return render_template('login.html')

# تسجيل حساب جديد
@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        role = request.form.get('role')

        # منع إنشاء حسابات إدارة من صفحة التسجيل العامة
        if role == Role.ADMIN:
            flash('لا يمكن إنشاء حسابات إدارة من هذه الصفحة', 'danger')
            return render_template('register.html')

        # التحقق من وجود اسم المستخدم أو البريد الإلكتروني
        if User.query.filter_by(username=username).first():
            flash('اسم المستخدم موجود بالفعل', 'danger')
            return render_template('register.html')

        if User.query.filter_by(email=email).first():
            flash('البريد الإلكتروني موجود بالفعل', 'danger')
            return render_template('register.html')

        # إنشاء مستخدم جديد (معطل بشكل افتراضي)
        hashed_password = generate_password_hash(password)
        new_user = User(
            username=username,
            email=email,
            password=hashed_password,
            role=role,
            _is_active=False  # الحساب معطل حتى يتم تفعيله من قبل الإدارة
        )

        db.session.add(new_user)
        db.session.commit()

        flash('تم إنشاء الحساب بنجاح! سيتم تفعيله من قبل الإدارة قريباً.', 'info')
        return redirect(url_for('login'))

    return render_template('register.html')

# لوحة التحكم
@app.route('/dashboard')
@login_required
def dashboard():
    if current_user.role == Role.ADMIN:
        return redirect(url_for('admin_dashboard'))
    elif current_user.role == Role.INSPECTOR:
        return redirect(url_for('inspector_dashboard'))
    else:
        return redirect(url_for('teacher_dashboard'))

# لوحة تحكم الإدارة
@app.route('/dashboard/admin')
@login_required
def admin_dashboard():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    inspectors = User.query.filter_by(role=Role.INSPECTOR).all()
    teachers = User.query.filter_by(role=Role.TEACHER).all()
    admins = User.query.filter_by(role=Role.ADMIN).all()

    # الحصول على الحسابات المعطلة (في انتظار التفعيل)
    pending_users = User.query.filter_by(_is_active=False).all()

    # الحصول على المواد الدراسية الفعلية من قواعد البيانات
    subjects_data = {}
    levels = EducationalLevel.query.all()

    # تحديد ألوان مميزة لكل مستوى تعليمي
    level_colors = {
        'السنة الأولى ابتدائي': {'color': 'primary', 'hex': '#0d6efd'},      # أزرق
        'السنة الثانية ابتدائي': {'color': 'success', 'hex': '#198754'},     # أخضر
        'السنة الثالثة ابتدائي': {'color': 'warning', 'hex': '#fd7e14'},     # برتقالي
        'السنة الرابعة ابتدائي': {'color': 'danger', 'hex': '#dc3545'},      # أحمر
        'السنة الخامسة ابتدائي': {'color': 'info', 'hex': '#0dcaf0'}        # سماوي
    }

    for level in levels:
        level_db = LevelDatabase.query.filter_by(level_id=level.id, is_active=True).first()
        if level_db:
            subjects = LevelDataEntry.query.filter_by(
                database_id=level_db.id,
                entry_type='subject',
                is_active=True
            ).all()

            subjects_data[level.name] = {
                'subjects': [],
                'level_color': level_colors.get(level.name, {'color': 'secondary', 'hex': '#6c757d'})
            }
            for subject in subjects:
                # حساب عدد المواد المعرفية لكل مادة دراسية
                domains = LevelDataEntry.query.filter_by(
                    database_id=level_db.id,
                    entry_type='domain',
                    parent_id=subject.id,
                    is_active=True
                ).all()

                materials_count = 0
                for domain in domains:
                    materials_count += LevelDataEntry.query.filter_by(
                        database_id=level_db.id,
                        entry_type='material',
                        parent_id=domain.id,
                        is_active=True
                    ).count()

                subjects_data[level.name]['subjects'].append({
                    'id': subject.id,
                    'name': subject.name,
                    'materials_count': materials_count,
                    'level_color': level_colors.get(level.name, {'color': 'secondary', 'hex': '#6c757d'})
                })

    return render_template('admin_dashboard.html',
                         inspectors=inspectors,
                         teachers=teachers,
                         admins=admins,
                         pending_users=pending_users,
                         subjects_data=subjects_data)

# إدارة المفتشين والأساتذة
@app.route('/admin/manage_inspectors')
@login_required
def manage_inspectors():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على جميع المفتشين
    inspectors = User.query.filter_by(role=Role.INSPECTOR).all()

    # الحصول على جميع الأساتذة
    teachers = User.query.filter_by(role=Role.TEACHER).all()

    # إنشاء قاموس للمفتشين والأساتذة تحت إشرافهم
    inspector_data = {}
    for inspector in inspectors:
        supervised_teachers = inspector.supervised_teachers.all()
        inspector_data[inspector.id] = {
            'inspector': inspector,
            'teachers': supervised_teachers,
            'teacher_count': len(supervised_teachers)
        }

    # الأساتذة غير المرتبطين بأي مفتش
    unassigned_teachers = []
    for teacher in teachers:
        if teacher.inspectors.count() == 0:
            unassigned_teachers.append(teacher)

    return render_template('manage_inspectors.html',
                         inspector_data=inspector_data,
                         unassigned_teachers=unassigned_teachers,
                         all_inspectors=inspectors)

# تعديل المفتش المشرف على الأستاذ
@app.route('/admin/assign_teacher_to_inspector', methods=['POST'])
@login_required
def assign_teacher_to_inspector():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    teacher_id = request.form.get('teacher_id')
    inspector_id = request.form.get('inspector_id')

    if not teacher_id:
        flash('يجب اختيار أستاذ', 'danger')
        return redirect(url_for('manage_inspectors'))

    try:
        teacher = User.query.filter_by(id=teacher_id, role=Role.TEACHER).first()
        if not teacher:
            flash('الأستاذ غير موجود', 'danger')
            return redirect(url_for('manage_inspectors'))

        # إزالة الأستاذ من أي مفتش سابق
        for inspector in teacher.inspectors:
            inspector.supervised_teachers.remove(teacher)

        # إضافة الأستاذ للمفتش الجديد (إذا تم اختيار مفتش)
        if inspector_id:
            new_inspector = User.query.filter_by(id=inspector_id, role=Role.INSPECTOR).first()
            if new_inspector:
                new_inspector.supervised_teachers.append(teacher)
                flash(f'تم تعيين الأستاذ {teacher.username} تحت إشراف المفتش {new_inspector.username}', 'success')
            else:
                flash('المفتش غير موجود', 'danger')
        else:
            flash(f'تم إلغاء إشراف الأستاذ {teacher.username}', 'info')

        db.session.commit()

    except Exception as e:
        db.session.rollback()
        print(f"Error assigning teacher to inspector: {str(e)}")
        flash('حدث خطأ أثناء تعديل الإشراف', 'danger')

    return redirect(url_for('manage_inspectors'))

# إدارة قواعد البيانات المنفصلة
@app.route('/admin/databases')
@login_required
def manage_databases():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    levels = EducationalLevel.query.all()
    databases = LevelDatabase.query.all()

    return render_template('manage_level_databases.html', levels=levels, databases=databases)

# إضافة قاعدة بيانات جديدة
@app.route('/admin/databases/add', methods=['POST'])
@login_required
def add_database():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    level_id = request.form.get('level_id')
    name = request.form.get('name')
    file_path = request.form.get('file_path')
    is_active = 'is_active' in request.form

    # التحقق من وجود المستوى
    level = EducationalLevel.query.get(level_id)
    if not level:
        flash('المستوى التعليمي غير موجود', 'danger')
        return redirect(url_for('manage_databases'))

    # إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
    data_dir = os.path.join(app.root_path, 'data')
    os.makedirs(data_dir, exist_ok=True)

    # إنشاء قاعدة بيانات جديدة
    new_db = LevelDatabase(
        level_id=level_id,
        name=name,
        file_path=file_path,
        is_active=is_active
    )

    db.session.add(new_db)
    db.session.commit()

    flash('تم إضافة قاعدة البيانات بنجاح', 'success')
    return redirect(url_for('manage_databases'))

# عرض قاعدة بيانات
@app.route('/admin/databases/<int:db_id>/view')
@login_required
def view_database(db_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)
    entries = LevelDataEntry.query.filter_by(database_id=db_id).all()

    return render_template('view_database.html', database=database, entries=entries)

# تعديل قاعدة بيانات
@app.route('/admin/databases/<int:db_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_database(db_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    if request.method == 'POST':
        database.name = request.form.get('name')
        database.file_path = request.form.get('file_path')
        database.is_active = 'is_active' in request.form

        db.session.commit()

        flash('تم تحديث قاعدة البيانات بنجاح', 'success')
        return redirect(url_for('manage_databases'))

    return render_template('edit_database.html', database=database)

# حذف قاعدة بيانات
@app.route('/admin/databases/<int:db_id>/delete', methods=['POST'])
@login_required
def delete_database(db_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    # حذف جميع العناصر أولاً
    LevelDataEntry.query.filter_by(database_id=db_id).delete()

    # حذف قاعدة البيانات
    db.session.delete(database)
    db.session.commit()

    flash('تم حذف قاعدة البيانات بنجاح', 'success')
    return redirect(url_for('manage_databases'))

# تفعيل/تعطيل قاعدة بيانات
@app.route('/admin/databases/<int:db_id>/toggle/<string:action>')
@login_required
def toggle_database(db_id, action):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    if action == 'activate':
        database.is_active = True
        flash('تم تفعيل قاعدة البيانات بنجاح', 'success')
    elif action == 'deactivate':
        database.is_active = False
        flash('تم تعطيل قاعدة البيانات بنجاح', 'success')

    db.session.commit()
    return redirect(url_for('manage_databases'))

# إدارة عناصر قاعدة البيانات
@app.route('/admin/databases/<int:db_id>/entries/add', methods=['POST'])
@login_required
def add_database_entry(db_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    entry_type = request.form.get('entry_type')
    parent_id = request.form.get('parent_id')
    is_active = 'is_active' in request.form
    is_multiple = 'is_multiple' in request.form

    # التحقق من نوع الإضافة (مفردة أو متعددة)
    if is_multiple:
        # إضافة متعددة
        multiple_entries = request.form.get('multiple_entries', '')
        entries_list = [entry.strip() for entry in multiple_entries.split('\n') if entry.strip()]

        added_count = 0
        for entry_name in entries_list:
            # التحقق من وجود العنصر
            existing_entry = LevelDataEntry.query.filter_by(
                database_id=db_id,
                entry_type=entry_type,
                parent_id=parent_id if parent_id != '0' else None,
                name=entry_name
            ).first()

            if not existing_entry:
                # إنشاء عنصر جديد
                new_entry = LevelDataEntry(
                    database_id=db_id,
                    entry_type=entry_type,
                    parent_id=parent_id if parent_id != '0' else None,
                    name=entry_name,
                    description=f"مضاف بواسطة الإضافة المتعددة",
                    is_active=is_active
                )
                db.session.add(new_entry)
                added_count += 1

        db.session.commit()

        # إرجاع استجابة JSON للطلبات المتعددة
        return jsonify({
            'success': True,
            'message': f'تم إضافة {added_count} عنصر بنجاح',
            'count': added_count
        })
    else:
        # إضافة مفردة
        name = request.form.get('name')
        description = request.form.get('description')

        # إنشاء عنصر جديد
        new_entry = LevelDataEntry(
            database_id=db_id,
            entry_type=entry_type,
            parent_id=parent_id if parent_id != '0' else None,
            name=name,
            description=description,
            is_active=is_active
        )

        db.session.add(new_entry)
        db.session.commit()

        flash('تم إضافة العنصر بنجاح', 'success')
    return redirect(url_for('view_database', db_id=db_id))

@app.route('/admin/databases/<int:db_id>/entries/<int:entry_id>/edit', methods=['POST'])
@login_required
def edit_database_entry(db_id, entry_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    entry = LevelDataEntry.query.get_or_404(entry_id)

    entry.name = request.form.get('name')
    entry.description = request.form.get('description')
    entry.is_active = 'is_active' in request.form

    db.session.commit()

    flash('تم تحديث العنصر بنجاح', 'success')
    return redirect(url_for('view_database', db_id=db_id))

@app.route('/admin/databases/<int:db_id>/entries/<int:entry_id>/delete', methods=['POST'])
@login_required
def delete_database_entry(db_id, entry_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    entry = LevelDataEntry.query.get_or_404(entry_id)

    # حذف جميع العناصر الفرعية بشكل متكرر
    delete_child_entries(db_id, entry_id)

    # حذف العنصر
    db.session.delete(entry)
    db.session.commit()

    flash('تم حذف العنصر بنجاح', 'success')
    return redirect(url_for('view_database', db_id=db_id))

# دالة مساعدة لحذف العناصر الفرعية بشكل متكرر
def delete_child_entries(db_id, parent_id):
    child_entries = LevelDataEntry.query.filter_by(database_id=db_id, parent_id=parent_id).all()

    for entry in child_entries:
        # حذف أبناء هذا العنصر
        delete_child_entries(db_id, entry.id)

        # حذف هذا العنصر
        db.session.delete(entry)

# لوحة تحكم المفتش (محسنة للأداء)
@app.route('/dashboard/inspector')
@login_required
def inspector_dashboard():
    """لوحة تحكم المفتش - مبسطة ومحسنة للأداء"""
    try:
        if current_user.role != Role.INSPECTOR:
            flash('غير مصرح بالوصول', 'danger')
            return redirect(url_for('dashboard'))

        # الحصول على الأساتذة تحت الإشراف
        teachers = current_user.supervised_teachers.all()

        # الحصول على الأساتذة المتاحين للإضافة
        supervised_teacher_ids = [t.id for t in teachers]
        if supervised_teacher_ids:
            available_teachers = User.query.filter(
                User.role == Role.TEACHER,
                ~User.id.in_(supervised_teacher_ids)
            ).all()
        else:
            available_teachers = User.query.filter_by(role=Role.TEACHER).all()

        # حساب إحصائيات بسيطة
        teacher_progress = {}
        for teacher in teachers:
            completed_count = ProgressEntry.query.filter_by(user_id=teacher.id, status='completed').count()
            in_progress_count = ProgressEntry.query.filter_by(user_id=teacher.id, status='in_progress').count()
            planned_count = ProgressEntry.query.filter_by(user_id=teacher.id, status='planned').count()
            total_count = completed_count + in_progress_count + planned_count
            completion_rate = (completed_count / total_count * 100) if total_count > 0 else 0

            teacher_progress[teacher.id] = {
                'stats': {
                    'completed': completed_count,
                    'in_progress': in_progress_count,
                    'planned': planned_count,
                    'total': total_count
                },
                'completion_rate': completion_rate
            }

        # إحصائيات بسيطة للعرض
        from datetime import date
        current_date = date.today().strftime('%Y-%m-%d')

        # قيم افتراضية بسيطة
        progress_stats = {'completed': 0, 'in_progress': 0, 'planned': 0, 'total': 0}
        overall_completion_rate = 0
        level_stats = {}
        subject_stats = {}
        today_completed_lessons = 0
        total_in_progress_lessons = 0
        daily_average_progress = 0
        best_teacher_this_month = None
        unread_notifications_count = 0

        # عرض القالب مع البيانات المبسطة
        return render_template('inspector_dashboard.html',
                               teachers=teachers,
                               available_teachers=available_teachers,
                               teacher_progress=teacher_progress,
                               progress_stats=progress_stats,
                               overall_completion_rate=overall_completion_rate,
                               level_stats=level_stats,
                               subject_stats=subject_stats,
                               today_completed_lessons=today_completed_lessons,
                               total_in_progress_lessons=total_in_progress_lessons,
                               daily_average_progress=daily_average_progress,
                               best_teacher_this_month=best_teacher_this_month,
                               unread_notifications_count=unread_notifications_count,
                               current_date=current_date)

    except Exception as e:
        # في حالة الخطأ، عرض صفحة بسيطة
        from datetime import date
        current_date = date.today().strftime('%Y-%m-%d')

        return render_template('inspector_dashboard.html',
                               teachers=[],
                               available_teachers=[],
                               teacher_progress={},
                               progress_stats={'completed': 0, 'in_progress': 0, 'planned': 0, 'total': 0},
                               overall_completion_rate=0,
                               level_stats={},
                               subject_stats={},
                               today_completed_lessons=0,
                               total_in_progress_lessons=0,
                               daily_average_progress=0,
                               best_teacher_this_month=None,
                               unread_notifications_count=0,
                               current_date=current_date)

# إضافة أستاذ تحت إشراف المفتش

@app.route('/inspector/add-teacher', methods=['POST'])
@login_required
def add_teacher_to_supervision():
    if current_user.role != Role.INSPECTOR:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    teacher_id = request.form.get('teacher_id')
    if not teacher_id:
        flash('يجب اختيار أستاذ', 'danger')
        return redirect(url_for('inspector_dashboard'))

    teacher = User.query.get(teacher_id)
    if not teacher or teacher.role != Role.TEACHER:
        flash('الأستاذ غير موجود', 'danger')
        return redirect(url_for('inspector_dashboard'))

    # إضافة الأستاذ تحت الإشراف
    current_user.supervised_teachers.append(teacher)
    db.session.commit()

    flash(f'تم إضافة الأستاذ {teacher.username} تحت إشرافك بنجاح', 'success')
    return redirect(url_for('inspector_dashboard'))

# إزالة أستاذ من الإشراف
@app.route('/api/remove_teacher_supervision/<int:teacher_id>', methods=['POST'])
@login_required
def remove_teacher_supervision(teacher_id):
    if current_user.role != Role.INSPECTOR:
        return jsonify({'error': 'غير مصرح بالوصول'}), 403

    try:
        # التحقق من أن الأستاذ تحت إشراف المفتش الحالي
        teacher = User.query.filter_by(id=teacher_id, role=Role.TEACHER).first()
        if not teacher:
            return jsonify({'error': 'الأستاذ غير موجود'}), 404

        # التحقق من الإشراف
        supervised_teachers = current_user.supervised_teachers.all()
        if teacher not in supervised_teachers:
            return jsonify({'error': 'الأستاذ ليس تحت إشرافك'}), 404

        # إزالة الإشراف
        current_user.supervised_teachers.remove(teacher)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'تم إزالة الأستاذ {teacher.username} من الإشراف بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'حدث خطأ: {str(e)}'}), 500

# عرض التقدم التفصيلي للأستاذ (آخر 5 دروس مكتملة)
@app.route('/api/teacher_detailed_progress/<int:teacher_id>')
@login_required
# دالة حساب التقدم الإجمالي للمفتش
def calculate_inspector_overall_completion_rate(inspector_id):
    """
    حساب نسبة الإنجاز الإجمالية للمفتش بناءً على تقدم جميع الأساتذة تحت إشرافه
    المعادلة مرنة وتتكيف مع إضافة أو حذف الأساتذة
    """
    try:
        # الحصول على الأساتذة تحت إشراف المفتش
        inspector = User.query.get(inspector_id)
        if not inspector or inspector.role != Role.INSPECTOR:
            return 0

        supervised_teachers = inspector.supervised_teachers.all()
        if not supervised_teachers:
            return 0

        total_completion_rates = []

        for teacher in supervised_teachers:
            # حساب نسبة إنجاز كل أستاذ
            completed_count = ProgressEntry.query.filter_by(
                user_id=teacher.id,
                status='completed'
            ).count()

            total_count = ProgressEntry.query.filter_by(user_id=teacher.id).count()

            if total_count > 0:
                teacher_completion_rate = (completed_count / total_count) * 100
                total_completion_rates.append(teacher_completion_rate)

        # حساب المتوسط
        if total_completion_rates:
            overall_rate = sum(total_completion_rates) / len(total_completion_rates)
            return round(overall_rate, 2)
        else:
            return 0

    except Exception as e:
        return 0

# عرض التقدم التفصيلي للأستاذ (آخر 5 دروس مكتملة)
@app.route('/api/teacher_detailed_progress/<int:teacher_id>')
@login_required
def get_teacher_detailed_progress(teacher_id):
    if current_user.role != Role.INSPECTOR:
        return jsonify({'error': 'غير مصرح بالوصول'}), 403

    # التحقق من أن الأستاذ تحت إشراف المفتش الحالي
    teacher = User.query.filter_by(id=teacher_id, role=Role.TEACHER).first()
    if not teacher:
        return jsonify({'error': 'الأستاذ غير موجود'}), 404

    # التحقق من الإشراف
    supervised_teachers = current_user.supervised_teachers.all()
    if teacher not in supervised_teachers:
        return jsonify({'error': 'الأستاذ ليس تحت إشرافك'}), 404

    try:
        # الحصول على آخر 5 دروس مكتملة
        completed_lessons = ProgressEntry.query.filter_by(
            user_id=teacher_id,
            status='completed'
        ).order_by(ProgressEntry.date.desc()).limit(5).all()

        lessons_data = []
        for progress in completed_lessons:
            lesson_info = {
                'id': progress.id,
                'date': progress.date.strftime('%Y-%m-%d') if progress.date else 'غير محدد',
                'notes': progress.notes or 'لا توجد ملاحظات',
                'level': 'غير محدد',
                'subject': 'غير محدد',
                'domain': 'غير محدد',
                'material': 'غير محدد',
                'competency': 'غير محدد'
            }
            lessons_data.append(lesson_info)

        # إحصائيات عامة
        total_completed = ProgressEntry.query.filter_by(
            user_id=teacher_id,
            status='completed'
        ).count()

        total_in_progress = ProgressEntry.query.filter_by(
            user_id=teacher_id,
            status='in_progress'
        ).count()

        total_planned = ProgressEntry.query.filter_by(
            user_id=teacher_id,
            status='planned'
        ).count()

        return jsonify({
            'success': True,
            'teacher_name': teacher.username,
            'recent_lessons': lessons_data,
            'stats': {
                'completed': total_completed,
                'in_progress': total_in_progress,
                'planned': total_planned,
                'total': total_completed + total_in_progress + total_planned
            }
        })

    except Exception as e:
        return jsonify({'error': f'حدث خطأ: {str(e)}'}), 500

# عرض الملف الشخصي للأستاذ (للقراءة فقط)
@app.route('/api/teacher_profile/<int:teacher_id>')
@login_required
def get_teacher_profile_readonly(teacher_id):
    if current_user.role != Role.INSPECTOR:
        return jsonify({'error': 'غير مصرح بالوصول'}), 403

    # التحقق من أن الأستاذ تحت إشراف المفتش الحالي
    teacher = User.query.filter_by(id=teacher_id, role=Role.TEACHER).first()
    if not teacher:
        return jsonify({'error': 'الأستاذ غير موجود'}), 404

    # التحقق من الإشراف
    supervised_teachers = current_user.supervised_teachers.all()
    if teacher not in supervised_teachers:
        return jsonify({'error': 'الأستاذ ليس تحت إشرافك'}), 404

    try:
        # معلومات الأستاذ
        profile_data = {
            'id': teacher.id,
            'username': teacher.username,
            'email': teacher.email,
            'full_name': getattr(teacher, 'full_name', 'غير محدد'),
            'phone': getattr(teacher, 'phone', 'غير محدد'),
            'address': getattr(teacher, 'address', 'غير محدد'),
            'created_at': teacher.created_at.strftime('%Y-%m-%d') if teacher.created_at else 'غير محدد',
            'updated_at': teacher.updated_at.strftime('%Y-%m-%d %H:%M') if teacher.updated_at else 'غير محدد',
            'last_login': getattr(teacher, 'last_login', 'لم يسجل دخول بعد'),
            'is_active': teacher.is_active
        }

        # إحصائيات التقدم
        completed_count = ProgressEntry.query.filter_by(
            user_id=teacher_id,
            status='completed'
        ).count()

        in_progress_count = ProgressEntry.query.filter_by(
            user_id=teacher_id,
            status='in_progress'
        ).count()

        planned_count = ProgressEntry.query.filter_by(
            user_id=teacher_id,
            status='planned'
        ).count()

        profile_data['progress_stats'] = {
            'completed': completed_count,
            'in_progress': in_progress_count,
            'planned': planned_count,
            'total': completed_count + in_progress_count + planned_count
        }

        # حساب نسبة الإنجاز
        if profile_data['progress_stats']['total'] > 0:
            profile_data['completion_rate'] = round(
                (completed_count / profile_data['progress_stats']['total']) * 100, 2
            )
        else:
            profile_data['completion_rate'] = 0

        return jsonify({
            'success': True,
            'profile': profile_data
        })

    except Exception as e:
        return jsonify({'error': f'حدث خطأ: {str(e)}'}), 500

if __name__ == '__main__':
    create_sample_data()
    app.run(debug=True)
