{% extends 'base.html' %}

{% block title %}لوحة تحكم المفتش{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- العنوان الرئيسي مع معلومات المفتش -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-user-tie me-2 text-primary"></i>
                لوحة تحكم المفتش
            </h1>
            <p class="text-muted mb-0">
                <i class="fas fa-user me-1"></i>
                مرحباً {{ current_user.username }}
                <span class="mx-2">|</span>
                <i class="fas fa-calendar me-1"></i>
                {{ moment().format('dddd، DD MMMM YYYY') if moment else 'اليوم' }}
            </p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTeacherModal">
                <i class="fas fa-user-plus me-1"></i>
                إضافة أستاذ
            </button>
            <button class="btn btn-outline-info" onclick="refreshDashboard()">
                <i class="fas fa-sync-alt me-1"></i>
                تحديث
            </button>
        </div>
    </div>

    <!-- البطاقات الإحصائية المحدثة -->
    <div class="row mb-4">
        <!-- إجمالي الأساتذة -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                الأساتذة تحت الإشراف
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ teachers|length }}</div>
                            <div class="text-xs text-muted">
                                {% if available_teachers|length > 0 %}
                                    <i class="fas fa-plus-circle text-success me-1"></i>
                                    {{ available_teachers|length }} متاح للإضافة
                                {% else %}
                                    <i class="fas fa-check-circle text-success me-1"></i>
                                    جميع الأساتذة مُعيَّنون
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chalkboard-teacher fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نسبة الإنجاز الإجمالية -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                نسبة الإنجاز الإجمالية
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ overall_completion_rate|default(0)|round|int }}%</div>
                            <div class="progress progress-sm mr-2">
                                <div class="progress-bar bg-success" role="progressbar" 
                                     style="width: {{ overall_completion_rate|default(0) }}%" 
                                     aria-valuenow="{{ overall_completion_rate|default(0) }}" 
                                     aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-pie fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- المهام المكتملة -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                المهام المكتملة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ progress_stats.completed|default(0) }}</div>
                            <div class="text-xs text-muted">
                                من أصل {{ progress_stats.total|default(0) }} مهمة
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- المهام قيد التنفيذ -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                قيد التنفيذ
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ progress_stats.in_progress|default(0) }}</div>
                            <div class="text-xs text-muted">
                                {{ ((progress_stats.in_progress|default(0) / progress_stats.total|default(1)) * 100)|round|int }}% من الإجمالي
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول الأساتذة تحت الإشراف -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-users me-2"></i>
                    الأساتذة تحت الإشراف ({{ teachers|length }})
                </h6>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addTeacherModal">
                        <i class="fas fa-user-plus me-1"></i>
                        إضافة أستاذ
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportTeachersData()">
                        <i class="fas fa-download me-1"></i>
                        تصدير
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            {% if teachers %}
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="teachersTable">
                        <thead class="table-light">
                            <tr>
                                <th style="color: black !important; background-color: #f8f9fa !important;">
                                    <i class="fas fa-user me-1"></i>
                                    الأستاذ
                                </th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">
                                    <i class="fas fa-envelope me-1"></i>
                                    البريد الإلكتروني
                                </th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">
                                    <i class="fas fa-chart-line me-1"></i>
                                    نسبة الإنجاز
                                </th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">
                                    <i class="fas fa-tasks me-1"></i>
                                    إحصائيات التقدم
                                </th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">
                                    <i class="fas fa-clock me-1"></i>
                                    آخر نشاط
                                </th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">
                                    <i class="fas fa-cogs me-1"></i>
                                    الإجراءات
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for teacher in teachers %}
                            <tr class="teacher-row" data-teacher-id="{{ teacher.id }}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm me-3">
                                            <div class="avatar-title bg-primary rounded-circle">
                                                {{ teacher.username[0].upper() }}
                                            </div>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ teacher.username }}</h6>
                                            <small class="text-muted">أستاذ</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="text-muted">{{ teacher.email }}</span>
                                </td>
                                <td>
                                    {% set completion_rate = teacher_progress[teacher.id]['completion_rate']|default(0) if teacher_progress and teacher.id in teacher_progress else 0 %}
                                    <div class="d-flex align-items-center">
                                        <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                            <div class="progress-bar 
                                                {% if completion_rate >= 80 %}bg-success
                                                {% elif completion_rate >= 60 %}bg-info  
                                                {% elif completion_rate >= 40 %}bg-warning
                                                {% else %}bg-danger{% endif %}" 
                                                role="progressbar" 
                                                style="width: {{ completion_rate }}%;" 
                                                aria-valuenow="{{ completion_rate }}" 
                                                aria-valuemin="0" 
                                                aria-valuemax="100">
                                            </div>
                                        </div>
                                        <span class="text-sm font-weight-bold">{{ completion_rate|round|int }}%</span>
                                    </div>
                                </td>
                                <td>
                                    {% if teacher_progress and teacher.id in teacher_progress %}
                                        <div class="d-flex flex-wrap gap-1">
                                            <span class="badge bg-success" title="مكتمل">
                                                <i class="fas fa-check me-1"></i>{{ teacher_progress[teacher.id]['stats']['completed'] }}
                                            </span>
                                            <span class="badge bg-warning" title="قيد التنفيذ">
                                                <i class="fas fa-clock me-1"></i>{{ teacher_progress[teacher.id]['stats']['in_progress'] }}
                                            </span>
                                            <span class="badge bg-danger" title="مخطط">
                                                <i class="fas fa-calendar me-1"></i>{{ teacher_progress[teacher.id]['stats']['planned'] }}
                                            </span>
                                        </div>
                                        <small class="text-muted d-block mt-1">
                                            إجمالي: {{ teacher_progress[teacher.id]['stats']['total'] }} مهمة
                                        </small>
                                    {% else %}
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-minus me-1"></i>لا يوجد تقدم
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if teacher.updated_at %}
                                        <span class="text-muted">{{ teacher.updated_at.strftime('%Y-%m-%d') }}</span>
                                        <small class="d-block text-muted">{{ teacher.updated_at.strftime('%H:%M') }}</small>
                                    {% else %}
                                        <span class="text-muted">لا يوجد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#teacherProgressModal" 
                                                data-teacher-id="{{ teacher.id }}" 
                                                data-teacher-name="{{ teacher.username }}"
                                                title="عرض التقدم التفصيلي">
                                            <i class="fas fa-chart-bar"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                onclick="viewTeacherProfile({{ teacher.id }})"
                                                title="عرض الملف الشخصي">
                                            <i class="fas fa-user"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="removeTeacher({{ teacher.id }}, '{{ teacher.username }}')"
                                                title="إزالة من الإشراف">
                                            <i class="fas fa-user-minus"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا يوجد أساتذة تحت الإشراف</h5>
                    <p class="text-muted mb-4">ابدأ بإضافة أساتذة لمتابعة تقدمهم</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTeacherModal">
                        <i class="fas fa-user-plus me-1"></i>
                        إضافة أول أستاذ
                    </button>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- الرسوم البيانية والإحصائيات التفصيلية -->
    <div class="row mb-4">
        <!-- رسم بياني للتقدم حسب المستوى -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقدم حسب المستوى التعليمي
                    </h6>
                </div>
                <div class="card-body">
                    {% if level_stats %}
                        <canvas id="levelProgressChart" width="400" height="200"></canvas>
                        <div class="mt-3">
                            {% for level_id, stats in level_stats.items() %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-sm">{{ stats.name }}</span>
                                <div class="d-flex align-items-center">
                                    <div class="progress me-2" style="width: 100px; height: 6px;">
                                        <div class="progress-bar bg-primary" role="progressbar"
                                             style="width: {{ stats.completion_rate|default(0) }}%"></div>
                                    </div>
                                    <span class="text-sm font-weight-bold">{{ stats.completion_rate|default(0)|round|int }}%</span>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-chart-bar fa-2x text-muted mb-2"></i>
                            <p class="text-muted">لا توجد بيانات للعرض</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- رسم بياني للتقدم حسب المادة -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie me-2"></i>
                        التقدم حسب المادة الدراسية
                    </h6>
                </div>
                <div class="card-body">
                    {% if subject_stats %}
                        <canvas id="subjectProgressChart" width="400" height="200"></canvas>
                        <div class="mt-3">
                            <div class="row">
                                {% for subject_id, stats in subject_stats.items() %}
                                <div class="col-6 mb-2">
                                    <div class="d-flex align-items-center">
                                        <div class="bg-primary rounded-circle me-2" style="width: 8px; height: 8px;"></div>
                                        <span class="text-sm">{{ stats.name }}</span>
                                        <span class="text-sm font-weight-bold ms-auto">{{ stats.completion_rate|default(0)|round|int }}%</span>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-chart-pie fa-2x text-muted mb-2"></i>
                            <p class="text-muted">لا توجد بيانات للعرض</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة إضافية -->
    <div class="row mb-4">
        <div class="col-lg-4 mb-4">
            <div class="card bg-gradient-primary text-white shadow">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-50 small">متوسط التقدم اليومي</div>
                            <div class="text-white h5 mb-0">
                                {{ ((progress_stats.completed|default(0) / 30)|round(1)) if progress_stats.completed else 0 }} مهمة/يوم
                            </div>
                        </div>
                        <div class="text-white-50">
                            <i class="fas fa-calendar-day fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card bg-gradient-success text-white shadow">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-50 small">أفضل أستاذ هذا الشهر</div>
                            <div class="text-white h6 mb-0">
                                {% if teachers %}
                                    {% set best_teacher = teachers|first %}
                                    {{ best_teacher.username }}
                                {% else %}
                                    لا يوجد
                                {% endif %}
                            </div>
                        </div>
                        <div class="text-white-50">
                            <i class="fas fa-trophy fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card bg-gradient-info text-white shadow">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-50 small">المهام المتبقية</div>
                            <div class="text-white h5 mb-0">
                                {{ (progress_stats.total|default(0) - progress_stats.completed|default(0)) }}
                            </div>
                        </div>
                        <div class="text-white-50">
                            <i class="fas fa-tasks fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة أستاذ -->
<div class="modal fade" id="addTeacherModal" tabindex="-1" aria-labelledby="addTeacherModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addTeacherModalLabel">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة أستاذ تحت الإشراف
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('add_teacher_to_supervision') }}" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="teacher_id" class="form-label">
                            <i class="fas fa-chalkboard-teacher me-1"></i>
                            اختر الأستاذ
                        </label>
                        <select class="form-select" id="teacher_id" name="teacher_id" required>
                            <option value="">-- اختر الأستاذ --</option>
                            {% for teacher in available_teachers %}
                            <option value="{{ teacher.id }}">
                                {{ teacher.username }} ({{ teacher.email }})
                            </option>
                            {% endfor %}
                        </select>
                        {% if not available_teachers %}
                        <div class="form-text text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            لا يوجد أساتذة متاحين للإضافة حالياً
                        </div>
                        {% endif %}
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>ملاحظة:</strong> ستتمكن من متابعة تقدم الأستاذ فور إضافته تحت إشرافك.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary" {% if not available_teachers %}disabled{% endif %}>
                        <i class="fas fa-plus me-1"></i>
                        إضافة الأستاذ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal عرض تقدم الأستاذ -->
<div class="modal fade" id="teacherProgressModal" tabindex="-1" aria-labelledby="teacherProgressModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="teacherProgressModalLabel">
                    <i class="fas fa-chart-line me-2"></i>
                    تقدم الأستاذ: <span id="modalTeacherName"></span>
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="teacherProgressContent">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-2">جاري تحميل بيانات التقدم...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="printTeacherProgress()">
                    <i class="fas fa-print me-1"></i>
                    طباعة التقرير
                </button>
            </div>
        </div>
    </div>
</div>

<!-- CSS إضافي -->
<style>
    .avatar-sm {
        width: 40px;
        height: 40px;
    }

    .avatar-title {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 14px;
    }

    .progress-sm {
        height: 6px;
    }

    .card.border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }

    .card.border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }

    .card.border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }

    .card.border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }

    .bg-gradient-primary {
        background: linear-gradient(87deg, #4e73df 0, #224abe 100%) !important;
    }

    .bg-gradient-success {
        background: linear-gradient(87deg, #1cc88a 0, #13855c 100%) !important;
    }

    .bg-gradient-info {
        background: linear-gradient(87deg, #36b9cc 0, #258391 100%) !important;
    }

    .teacher-row:hover {
        background-color: rgba(0, 123, 255, 0.05);
        transform: translateY(-1px);
        transition: all 0.2s ease;
    }

    .btn-group .btn {
        margin: 0 1px;
    }
</style>

<!-- JavaScript -->
<script>
// تحديث لوحة التحكم
function refreshDashboard() {
    location.reload();
}

// تصدير بيانات الأساتذة
function exportTeachersData() {
    // يمكن تطوير هذه الوظيفة لاحقاً
    alert('ميزة التصدير قيد التطوير');
}

// عرض ملف الأستاذ الشخصي
function viewTeacherProfile(teacherId) {
    // إنشاء modal للملف الشخصي
    const modalHtml = `
        <div class="modal fade" id="teacherProfileModal" tabindex="-1" aria-labelledby="teacherProfileModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title" id="teacherProfileModalLabel">
                            <i class="fas fa-user me-2"></i>
                            الملف الشخصي للأستاذ
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div id="teacherProfileContent">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">جاري التحميل...</span>
                                </div>
                                <p class="mt-2">جاري تحميل الملف الشخصي...</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إزالة modal سابق إن وجد
    const existingModal = document.getElementById('teacherProfileModal');
    if (existingModal) {
        existingModal.remove();
    }

    // إضافة modal جديد
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // عرض modal
    const modal = new bootstrap.Modal(document.getElementById('teacherProfileModal'));
    modal.show();

    // تحميل البيانات
    fetch(`/inspector/teacher_profile/${teacherId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                document.getElementById('teacherProfileContent').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        ${data.error}
                    </div>
                `;
                return;
            }

            document.getElementById('teacherProfileContent').innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0"><i class="fas fa-info-circle me-1"></i> المعلومات الأساسية</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>الاسم:</strong> ${data.teacher_name}</p>
                                <p><strong>البريد الإلكتروني:</strong> ${data.teacher_email}</p>
                                <p><strong>تاريخ التسجيل:</strong> ${data.created_at}</p>
                                <p><strong>آخر نشاط:</strong> ${data.last_activity}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="fas fa-chart-pie me-1"></i> إحصائيات التقدم</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>إجمالي السجلات:</strong> ${data.total_progress}</p>
                                <p><strong>السجلات المكتملة:</strong> ${data.completed_progress}</p>
                                <p><strong>نسبة الإنجاز:</strong> ${data.completion_rate}%</p>
                                <div class="progress mt-2">
                                    <div class="progress-bar bg-success" role="progressbar"
                                         style="width: ${data.completion_rate}%"
                                         aria-valuenow="${data.completion_rate}"
                                         aria-valuemin="0" aria-valuemax="100">
                                        ${data.completion_rate}%
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('teacherProfileContent').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    حدث خطأ أثناء تحميل البيانات
                </div>
            `;
        });
}

// إزالة أستاذ من الإشراف
function removeTeacher(teacherId, teacherName) {
    if (confirm(`هل أنت متأكد من إزالة الأستاذ ${teacherName} من إشرافك؟`)) {
        // إنشاء form وإرساله
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/inspector/remove-teacher/${teacherId}`;
        document.body.appendChild(form);
        form.submit();
    }
}

// عرض تقدم الأستاذ في Modal
document.addEventListener('DOMContentLoaded', function() {
    const teacherProgressModal = document.getElementById('teacherProgressModal');
    if (teacherProgressModal) {
        teacherProgressModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const teacherId = button.getAttribute('data-teacher-id');
            const teacherName = button.getAttribute('data-teacher-name');

            document.getElementById('modalTeacherName').textContent = teacherName;

            // تحميل بيانات التقدم (يمكن تطويرها لاحقاً)
            setTimeout(() => {
                document.getElementById('teacherProgressContent').innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        تفاصيل تقدم الأستاذ ${teacherName} ستظهر هنا قريباً.
                    </div>
                `;
            }, 1000);
        });
    }
});

// طباعة تقرير التقدم
function printTeacherProgress() {
    const modalContent = document.getElementById('teacherProgressContent');
    const teacherName = document.getElementById('modalTeacherName').textContent;

    if (!modalContent) {
        alert('لا توجد بيانات للطباعة');
        return;
    }

    // إنشاء نافذة طباعة جديدة
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>تقرير تقدم الأستاذ - ${teacherName}</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
                .print-header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #007bff; padding-bottom: 15px; }
                .print-date { text-align: left; margin-bottom: 20px; color: #666; }
                @media print {
                    .btn { display: none !important; }
                    .modal-footer { display: none !important; }
                }
            </style>
        </head>
        <body>
            <div class="container-fluid">
                <div class="print-header">
                    <h2>تقرير تقدم الأستاذ</h2>
                    <h4>${teacherName}</h4>
                </div>
                <div class="print-date">
                    تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}
                </div>
                ${modalContent.innerHTML}
            </div>
            <script>
                window.onload = function() {
                    window.print();
                    window.onafterprint = function() {
                        window.close();
                    };
                };
            </script>
        </body>
        </html>
    `);
    printWindow.document.close();
}

// رسم بياني للتقدم حسب المستوى (Chart.js)
{% if level_stats %}
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('levelProgressChart');
    if (ctx) {
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: [{% for level_id, stats in level_stats.items() %}'{{ stats.name }}'{% if not loop.last %},{% endif %}{% endfor %}],
                datasets: [{
                    label: 'نسبة الإنجاز (%)',
                    data: [{% for level_id, stats in level_stats.items() %}{{ stats.completion_rate|default(0) }}{% if not loop.last %},{% endif %}{% endfor %}],
                    backgroundColor: 'rgba(78, 115, 223, 0.8)',
                    borderColor: 'rgba(78, 115, 223, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    }
});

// إضافة event listener لـ modal عرض التقدم
document.addEventListener('DOMContentLoaded', function() {
    const teacherProgressModal = document.getElementById('teacherProgressModal');
    if (teacherProgressModal) {
        teacherProgressModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const teacherId = button.getAttribute('data-teacher-id');
            const teacherName = button.getAttribute('data-teacher-name');

            // تحديث اسم الأستاذ في العنوان
            document.getElementById('modalTeacherName').textContent = teacherName;

            // تحميل بيانات التقدم
            fetch(`/inspector/teacher_progress/${teacherId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        document.getElementById('teacherProgressContent').innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                ${data.error}
                            </div>
                        `;
                        return;
                    }

                    // عرض البيانات
                    let progressHtml = `
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0"><i class="fas fa-chart-pie me-1"></i> نظرة عامة</h6>
                                    </div>
                                    <div class="card-body">
                                        <p><strong>الأستاذ:</strong> ${data.teacher_name}</p>
                                        <p><strong>البريد الإلكتروني:</strong> ${data.teacher_email}</p>
                                        <p><strong>نسبة الإنجاز الإجمالية:</strong> ${data.overall_progress.overall_percentage || 0}%</p>
                                        <div class="progress">
                                            <div class="progress-bar bg-info" role="progressbar"
                                                 style="width: ${data.overall_progress.overall_percentage || 0}%"
                                                 aria-valuenow="${data.overall_progress.overall_percentage || 0}"
                                                 aria-valuemin="0" aria-valuemax="100">
                                                ${data.overall_progress.overall_percentage || 0}%
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0"><i class="fas fa-tasks me-1"></i> إحصائيات التقدم</h6>
                                    </div>
                                    <div class="card-body">
                                        <p><strong>مكتمل:</strong> ${data.overall_progress.completed || 0}</p>
                                        <p><strong>قيد التنفيذ:</strong> ${data.overall_progress.in_progress || 0}</p>
                                        <p><strong>مخطط:</strong> ${data.overall_progress.planned || 0}</p>
                                        <p><strong>الإجمالي:</strong> ${data.overall_progress.total || 0}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-history me-1"></i> آخر 10 سجلات تقدم</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped">
                                        <thead class="table-light">
                                            <tr>
                                                <th style="color: black !important; background-color: #f8f9fa !important;">التاريخ</th>
                                                <th style="color: black !important; background-color: #f8f9fa !important;">المستوى</th>
                                                <th style="color: black !important; background-color: #f8f9fa !important;">المادة</th>
                                                <th style="color: black !important; background-color: #f8f9fa !important;">الميدان</th>
                                                <th style="color: black !important; background-color: #f8f9fa !important;">المادة المعرفية</th>
                                                <th style="color: black !important; background-color: #f8f9fa !important;">الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                    `;

                    if (data.recent_progress && data.recent_progress.length > 0) {
                        data.recent_progress.forEach(entry => {
                            const statusClass = entry.status === 'completed' ? 'success' :
                                              entry.status === 'in_progress' ? 'warning' : 'danger';
                            progressHtml += `
                                <tr>
                                    <td>${entry.date}</td>
                                    <td>${entry.level}</td>
                                    <td>${entry.subject}</td>
                                    <td>${entry.domain}</td>
                                    <td>${entry.material}</td>
                                    <td><span class="badge bg-${statusClass}">${entry.status_text}</span></td>
                                </tr>
                            `;
                        });
                    } else {
                        progressHtml += `
                            <tr>
                                <td colspan="6" class="text-center text-muted">لا توجد سجلات تقدم</td>
                            </tr>
                        `;
                    }

                    progressHtml += `
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    `;

                    document.getElementById('teacherProgressContent').innerHTML = progressHtml;
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('teacherProgressContent').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            حدث خطأ أثناء تحميل البيانات
                        </div>
                    `;
                });
        });
    }
});
{% endif %}
</script>
{% endblock %}
