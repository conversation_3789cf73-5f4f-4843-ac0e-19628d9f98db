{% extends 'base.html' %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">لوحة تحكم المفتش</h2>
    </div>
</div>

<div class="row">
    <div class="col-xl-3 col-md-6">
        <div class="card bg-primary text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>الأساتذة</div>
                    <div class="h3">{{ teachers|length }}</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#teachersModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-success text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>نسبة الإنجاز</div>
                    <div class="h3">{{ overall_completion_rate|default(0)|round|int }}%</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#completionRateModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-warning text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>قيد التنفيذ</div>
                    <div class="h3">{{ (progress_stats.in_progress / progress_stats.total * 100)|round|int if progress_stats.total > 0 else 0 }}%</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#inProgressModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-danger text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>متأخر</div>
                    <div class="h3">{{ (progress_stats.planned / progress_stats.total * 100)|round|int if progress_stats.total > 0 else 0 }}%</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#delayedModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-chalkboard-teacher me-1"></i>
                        الأساتذة تحت الإشراف
                    </div>
                    <div>
                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addTeacherModal">
                            <i class="fas fa-plus me-1"></i> إضافة أستاذ
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="teachersTable">
                        <thead>
                            <tr>
                                <th>اسم المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>نسبة الإنجاز (المؤشر)</th>
                                <th>آخر تحديث</th>
                                <th>إحصائيات التقدم</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if teachers %}
                                {% for teacher in teachers %}
                                <tr>
                                    <td>{{ teacher.username }}</td>
                                    <td>{{ teacher.email }}</td>
                                    <td>
                                        {% if teacher_progress is defined and teacher_progress and teacher.id in teacher_progress %}
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-success" role="progressbar" style="width: {{ teacher_progress[teacher.id]['completion_rate']|default(0)|round|int }}%;" aria-valuenow="{{ teacher_progress[teacher.id]['completion_rate']|default(0)|round|int }}" aria-valuemin="0" aria-valuemax="100">{{ teacher_progress[teacher.id]['completion_rate']|default(0)|round|int }}%</div>
                                            </div>
                                        {% else %}
                                            <div class="progress">
                                                <div class="progress-bar bg-secondary" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                                            </div>
                                        {% endif %}
                                    </td>
                                    <td>{{ teacher.updated_at|default('لا يوجد', true) }}</td>
                                    <td>
                                        {% if teacher_progress is defined and teacher_progress and teacher.id in teacher_progress %}
                                            <div class="small">
                                                <span class="badge bg-success">مكتمل: {{ teacher_progress[teacher.id]['stats']['completed'] }}</span>
                                                <span class="badge bg-warning">قيد التنفيذ: {{ teacher_progress[teacher.id]['stats']['in_progress'] }}</span>
                                                <span class="badge bg-danger">مخطط: {{ teacher_progress[teacher.id]['stats']['planned'] }}</span>
                                                <span class="badge bg-info">المجموع: {{ teacher_progress[teacher.id]['stats']['total'] }}</span>
                                            </div>
                                        {% else %}
                                            <span class="badge bg-secondary">لا يوجد تقدم</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-primary view-progress" data-bs-toggle="modal" data-bs-target="#teacherProgressModal" data-teacher-id="{{ teacher.id }}" data-teacher-name="{{ teacher.username }}">
                                                <i class="fas fa-eye"></i> عرض التقدم
                                            </button>
                                            <form action="{{ url_for('remove_teacher_from_supervision', teacher_id=teacher.id) }}" method="post" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من إزالة الأستاذ {{ teacher.username }} من إشرافك؟')">
                                                <button type="submit" class="btn btn-sm btn-outline-danger">
                                                    <i class="fas fa-user-minus"></i> إزالة
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="6" class="text-center">لا يوجد أساتذة تحت الإشراف حالياً</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-chart-pie me-1"></i>
                نسبة الإنجاز حسب المستوى التعليمي
            </div>
            <div class="card-body">
                <canvas id="levelProgressChart" width="100%" height="50"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-chart-bar me-1"></i>
                نسبة الإنجاز حسب المادة
            </div>
            <div class="card-body">
                <canvas id="subjectProgressChart" width="100%" height="50"></canvas>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Sample data for charts
    document.addEventListener('DOMContentLoaded', function() {
        // Level progress chart
        const levelCtx = document.getElementById('levelProgressChart');
        const levelChart = new Chart(levelCtx, {
            type: 'pie',
            data: {
                labels: ['السنة الأولى', 'السنة الثانية', 'السنة الثالثة', 'السنة الرابعة', 'السنة الخامسة'],
                datasets: [{
                    data: [70, 65, 60, 55, 50],
                    backgroundColor: [
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(153, 102, 255, 0.8)',
                        'rgba(255, 159, 64, 0.8)',
                        'rgba(255, 99, 132, 0.8)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });

        // Subject progress chart
        const subjectCtx = document.getElementById('subjectProgressChart');
        const subjectChart = new Chart(subjectCtx, {
            type: 'bar',
            data: {
                labels: ['اللغة العربية', 'الرياضيات', 'التربية العلمية', 'التربية الإسلامية', 'التربية المدنية'],
                datasets: [{
                    label: 'نسبة الإنجاز',
                    data: [75, 68, 60, 80, 65],
                    backgroundColor: 'rgba(54, 162, 235, 0.8)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    });
</script>
{% endblock %}

<!-- Modal para mostrar el progreso del profesor -->
<div class="modal fade" id="teacherProgressModal" tabindex="-1" aria-labelledby="teacherProgressModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="teacherProgressModalLabel">تقدم الأستاذ: <span id="teacherName"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h6>ملخص التقدم</h6>
                        <div class="progress mb-2" style="height: 25px;">
                            <div id="teacherProgressBar" class="progress-bar bg-success" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <div>
                                <span class="badge bg-success">مكتمل</span> <span id="completedCount">0</span>
                            </div>
                            <div>
                                <span class="badge bg-warning">قيد التنفيذ</span> <span id="inProgressCount">0</span>
                            </div>
                            <div>
                                <span class="badge bg-danger">مخطط</span> <span id="plannedCount">0</span>
                            </div>
                            <div>
                                <span class="badge bg-info">المجموع</span> <span id="totalCount">0</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <h6>آخر التحديثات</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>المستوى</th>
                                        <th>المادة</th>
                                        <th>الميدان</th>
                                        <th>المادة المعرفية</th>
                                        <th>الكفاءة</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="recentEntriesTable">
                                    <!-- Se llenará dinámicamente con JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Datos de progreso de los profesores
        const teacherProgress = {
            {% if teacher_progress is defined and teacher_progress %}
                {% for teacher_id, progress in teacher_progress.items() %}
                "{{ teacher_id }}": {
                    "completion_rate": {{ progress.completion_rate|default(0)|round|int }},
                    "stats": {
                        "completed": {{ progress.stats.completed }},
                        "in_progress": {{ progress.stats.in_progress }},
                        "planned": {{ progress.stats.planned }},
                        "total": {{ progress.stats.total }}
                    },
                    "recent_entries": [
                        {% for entry in progress.recent_entries %}
                        {
                            "id": {{ entry.id }},
                            "date": "{{ entry.date.strftime('%Y-%m-%d') if entry.date else '' }}",
                            "status": "{{ entry.status }}",
                            "notes": "{{ entry.notes|default('', true) }}",
                            "level": {% if entry.level %}{
                                "id": {{ entry.level.id }},
                                "name": "{{ entry.level.name }}"
                            }{% else %}null{% endif %},
                            "subject": {% if entry.subject %}{
                                "id": {{ entry.subject.id }},
                                "name": "{{ entry.subject.name }}"
                            }{% else %}null{% endif %},
                            "domain": {% if entry.domain %}{
                                "id": {{ entry.domain.id }},
                                "name": "{{ entry.domain.name }}"
                            }{% else %}null{% endif %},
                            "material": {% if entry.material %}{
                                "id": {{ entry.material.id }},
                                "name": "{{ entry.material.name }}"
                            }{% else %}null{% endif %},
                            "competency": {% if entry.competency %}{
                                "id": {{ entry.competency.id }},
                                "name": "{{ entry.competency.name|default('', true) }}",
                                "description": "{{ entry.competency.description|default('', true) }}"
                            }{% else %}null{% endif %}
                        }{% if not loop.last %},{% endif %}
                        {% endfor %}
                    ]
                }{% if not loop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        };

        // Configurar el modal de progreso del profesor
        const viewProgressButtons = document.querySelectorAll('.view-progress');
        viewProgressButtons.forEach(button => {
            button.addEventListener('click', function() {
                const teacherId = this.getAttribute('data-teacher-id');
                const teacherName = this.getAttribute('data-teacher-name');

                // Actualizar el título del modal
                document.getElementById('teacherName').textContent = teacherName;

                // Verificar si hay datos de progreso para este profesor
                if (teacherProgress[teacherId]) {
                    const progress = teacherProgress[teacherId];

                    // Actualizar la barra de progreso
                    const progressBar = document.getElementById('teacherProgressBar');
                    progressBar.style.width = progress.completion_rate + '%';
                    progressBar.setAttribute('aria-valuenow', progress.completion_rate);
                    progressBar.textContent = progress.completion_rate + '%';

                    // Actualizar los contadores
                    document.getElementById('completedCount').textContent = progress.stats.completed;
                    document.getElementById('inProgressCount').textContent = progress.stats.in_progress;
                    document.getElementById('plannedCount').textContent = progress.stats.planned;
                    document.getElementById('totalCount').textContent = progress.stats.total;

                    // Actualizar la tabla de entradas recientes
                    const recentEntriesTable = document.getElementById('recentEntriesTable');
                    recentEntriesTable.innerHTML = '';

                    if (progress.recent_entries.length > 0) {
                        progress.recent_entries.forEach(entry => {
                            const row = document.createElement('tr');

                            // التاريخ
                            const dateCell = document.createElement('td');
                            dateCell.textContent = entry.date;
                            row.appendChild(dateCell);

                            // المستوى
                            const levelCell = document.createElement('td');
                            levelCell.textContent = entry.level ? entry.level.name : 'غير محدد';
                            row.appendChild(levelCell);

                            // المادة
                            const subjectCell = document.createElement('td');
                            subjectCell.textContent = entry.subject ? entry.subject.name : 'غير محدد';
                            row.appendChild(subjectCell);

                            // الميدان
                            const domainCell = document.createElement('td');
                            domainCell.textContent = entry.domain ? entry.domain.name : 'غير محدد';
                            row.appendChild(domainCell);

                            // المادة المعرفية
                            const materialCell = document.createElement('td');
                            materialCell.textContent = entry.material ? entry.material.name : 'غير محدد';
                            row.appendChild(materialCell);

                            // الكفاءة
                            const competencyCell = document.createElement('td');
                            if (entry.competency) {
                                const competencyText = entry.competency.description || entry.competency.name || 'بدون وصف';
                                competencyCell.textContent = competencyText.length > 50 ? competencyText.substring(0, 50) + '...' : competencyText;
                                competencyCell.title = competencyText; // للعرض عند تمرير الماوس
                            } else {
                                competencyCell.textContent = 'غير محدد';
                            }
                            row.appendChild(competencyCell);

                            // الحالة
                            const statusCell = document.createElement('td');
                            const statusBadge = document.createElement('span');
                            statusBadge.classList.add('badge');

                            if (entry.status === 'completed') {
                                statusBadge.classList.add('bg-success');
                                statusBadge.textContent = 'مكتمل';
                            } else if (entry.status === 'in_progress') {
                                statusBadge.classList.add('bg-warning');
                                statusBadge.textContent = 'قيد التنفيذ';
                            } else {
                                statusBadge.classList.add('bg-danger');
                                statusBadge.textContent = 'مخطط';
                            }

                            statusCell.appendChild(statusBadge);
                            row.appendChild(statusCell);

                            // إضافة خلية الإجراءات
                            const actionsCell = document.createElement('td');

                            // إنشاء زر التعديل
                            const editButton = document.createElement('button');
                            editButton.classList.add('btn', 'btn-sm', 'btn-primary', 'me-1');
                            editButton.innerHTML = '<i class="fas fa-edit"></i> تعديل';

                            // إضافة معلومات السجل كسمات للزر
                            editButton.setAttribute('data-entry-id', entry.id);
                            editButton.setAttribute('data-entry-date', entry.date);
                            editButton.setAttribute('data-entry-status', entry.status);
                            editButton.setAttribute('data-entry-notes', entry.notes || '');

                            if (entry.level) {
                                editButton.setAttribute('data-level-id', entry.level.id);
                                editButton.setAttribute('data-level-name', entry.level.name);
                            }

                            if (entry.subject) {
                                editButton.setAttribute('data-subject-id', entry.subject.id);
                                editButton.setAttribute('data-subject-name', entry.subject.name);
                            }

                            if (entry.domain) {
                                editButton.setAttribute('data-domain-id', entry.domain.id);
                                editButton.setAttribute('data-domain-name', entry.domain.name);
                            }

                            if (entry.material) {
                                editButton.setAttribute('data-material-id', entry.material.id);
                                editButton.setAttribute('data-material-name', entry.material.name);
                            }

                            if (entry.competency) {
                                editButton.setAttribute('data-competency-id', entry.competency.id);
                                editButton.setAttribute('data-competency-name', entry.competency.name || '');
                                editButton.setAttribute('data-competency-description', entry.competency.description || '');
                            }

                            // إضافة حدث النقر لفتح مودال التعديل
                            editButton.addEventListener('click', function() {
                                // هنا يمكن إضافة كود لفتح مودال التعديل وملئه بالبيانات
                                alert('سيتم تنفيذ وظيفة التعديل لاحقاً');
                            });

                            actionsCell.appendChild(editButton);

                            // إضافة خلية الإجراءات إلى الصف
                            row.appendChild(actionsCell);

                            recentEntriesTable.appendChild(row);
                        });
                    } else {
                        const row = document.createElement('tr');
                        const cell = document.createElement('td');
                        cell.setAttribute('colspan', '8');
                        cell.classList.add('text-center');
                        cell.textContent = 'لا توجد سجلات تقدم حتى الآن';
                        row.appendChild(cell);
                        recentEntriesTable.appendChild(row);
                    }
                } else {
                    // No hay datos de progreso para este profesor
                    document.getElementById('teacherProgressBar').style.width = '0%';
                    document.getElementById('teacherProgressBar').setAttribute('aria-valuenow', 0);
                    document.getElementById('teacherProgressBar').textContent = '0%';

                    document.getElementById('completedCount').textContent = '0';
                    document.getElementById('inProgressCount').textContent = '0';
                    document.getElementById('plannedCount').textContent = '0';
                    document.getElementById('totalCount').textContent = '0';

                    const recentEntriesTable = document.getElementById('recentEntriesTable');
                    recentEntriesTable.innerHTML = '';

                    const row = document.createElement('tr');
                    const cell = document.createElement('td');
                    cell.setAttribute('colspan', '8');
                    cell.classList.add('text-center');
                    cell.textContent = 'لا توجد سجلات تقدم حتى الآن';
                    row.appendChild(cell);
                    recentEntriesTable.appendChild(row);
                }
            });
        });
    });
</script>

<!-- Modal para añadir profesor -->
<div class="modal fade" id="addTeacherModal" tabindex="-1" aria-labelledby="addTeacherModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addTeacherModalLabel">إضافة أستاذ تحت الإشراف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="/inspector/add-teacher" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="teacher_id" class="form-label">اختر الأستاذ</label>
                        <select class="form-select" id="teacher_id" name="teacher_id" required>
                            <option value="">اختر الأستاذ</option>
                            {% for teacher in available_teachers %}
                            <option value="{{ teacher.id }}">{{ teacher.username }} ({{ teacher.email }})</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal for Teachers Details -->
<div class="modal fade" id="teachersModal" tabindex="-1" aria-labelledby="teachersModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="teachersModalLabel">تفاصيل الأساتذة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th>المستوى</th>
                                <th>نسبة الإنجاز</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for teacher in teachers %}
                            <tr>
                                <td>{{ teacher.name }}</td>
                                <td>{{ teacher.email }}</td>
                                <td>
                                    {% if teacher.levels %}
                                        {% for level in teacher.levels %}
                                            <span class="badge bg-info">{{ level.name }}</span>
                                        {% endfor %}
                                    {% else %}
                                        <span class="badge bg-secondary">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="progress">
                                        {% if teacher_progress is defined and teacher_progress and teacher.id in teacher_progress %}
                                            <div class="progress-bar bg-success" role="progressbar" style="width: {{ teacher_progress[teacher.id]['completion_rate']|default(0)|round|int }}%;" aria-valuenow="{{ teacher_progress[teacher.id]['completion_rate']|default(0)|round|int }}" aria-valuemin="0" aria-valuemax="100">{{ teacher_progress[teacher.id]['completion_rate']|default(0)|round|int }}%</div>
                                        {% else %}
                                            <div class="progress-bar bg-success" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('teacher_progress_view', teacher_id=teacher.id) }}" class="btn btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-info send-notification" data-id="{{ teacher.id }}" data-name="{{ teacher.name }}">
                                            <i class="fas fa-bell"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Completion Rate Details -->
<div class="modal fade" id="completionRateModal" tabindex="-1" aria-labelledby="completionRateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="completionRateModalLabel">تفاصيل نسبة الإنجاز</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h5>نسبة الإنجاز الإجمالية: {{ overall_completion_rate|default(0)|round|int }}%</h5>
                        <div class="progress" style="height: 25px;">
                            <div class="progress-bar bg-success" role="progressbar" style="width: {{ overall_completion_rate|default(0)|round|int }}%;" aria-valuenow="{{ overall_completion_rate|default(0)|round|int }}" aria-valuemin="0" aria-valuemax="100">{{ overall_completion_rate|default(0)|round|int }}%</div>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-12">
                        <h5>
                            <i class="fas fa-graduation-cap me-2"></i>
                            نسبة الإنجاز حسب المستوى (بناءً على المواد المعرفية)
                        </h5>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="color: white !important;">المستوى</th>
                                        <th style="color: white !important;">نسبة الإنجاز</th>
                                        <th style="color: white !important;">مواد معرفية مكتملة</th>
                                        <th style="color: white !important;">إجمالي المواد المعرفية</th>
                                        <th style="color: white !important;">التقدم التقليدي</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for level_id, level_data in level_stats.items() %}
                                    <tr>
                                        <td>
                                            <i class="fas fa-school me-1 text-primary"></i>
                                            {{ level_data.name }}
                                        </td>
                                        <td>
                                            <div class="progress" style="height: 25px;">
                                                <div class="progress-bar
                                                    {% if level_data.completion_rate >= 80 %}bg-success
                                                    {% elif level_data.completion_rate >= 60 %}bg-warning
                                                    {% elif level_data.completion_rate >= 40 %}bg-info
                                                    {% else %}bg-danger
                                                    {% endif %}"
                                                    role="progressbar"
                                                    style="width: {{ level_data.completion_rate|default(0)|round|int }}%;"
                                                    aria-valuenow="{{ level_data.completion_rate|default(0)|round|int }}"
                                                    aria-valuemin="0"
                                                    aria-valuemax="100">
                                                    {{ level_data.completion_rate|default(0)|round|int }}%
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">{{ level_data.completed_materials|default(0) }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ level_data.total_materials|default(0) }}</span>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                مكتمل: {{ level_data.stats.completed }},
                                                قيد التنفيذ: {{ level_data.stats.in_progress }},
                                                مخطط: {{ level_data.stats.planned }},
                                                المجموع: {{ level_data.stats.total }}
                                            </small>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                    {% if not level_stats %}
                                    <tr>
                                        <td colspan="5" class="text-center">لا توجد بيانات متاحة</td>
                                    </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <h5>
                            <i class="fas fa-book-open me-2"></i>
                            نسبة الإنجاز حسب المادة (بناءً على المواد المعرفية)
                        </h5>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="color: white !important;">المادة</th>
                                        <th style="color: white !important;">نسبة الإنجاز</th>
                                        <th style="color: white !important;">مواد معرفية مكتملة</th>
                                        <th style="color: white !important;">إجمالي المواد المعرفية</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for subject_name, subject_data in subject_stats.items() %}
                                    <tr>
                                        <td>
                                            <i class="fas fa-book me-1 text-primary"></i>
                                            {{ subject_name }}
                                        </td>
                                        <td>
                                            <div class="progress" style="height: 25px;">
                                                <div class="progress-bar
                                                    {% if subject_data.completion_rate >= 80 %}bg-success
                                                    {% elif subject_data.completion_rate >= 60 %}bg-warning
                                                    {% elif subject_data.completion_rate >= 40 %}bg-info
                                                    {% else %}bg-danger
                                                    {% endif %}"
                                                    role="progressbar"
                                                    style="width: {{ subject_data.completion_rate|round|int }}%;"
                                                    aria-valuenow="{{ subject_data.completion_rate|round|int }}"
                                                    aria-valuemin="0"
                                                    aria-valuemax="100">
                                                    {{ subject_data.completion_rate|round|int }}%
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">{{ subject_data.completed_materials }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ subject_data.total_materials }}</span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                    {% if not subject_stats %}
                                    <tr>
                                        <td colspan="4" class="text-center">لا توجد بيانات متاحة</td>
                                    </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for In Progress Tasks -->
<div class="modal fade" id="inProgressModal" tabindex="-1" aria-labelledby="inProgressModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="inProgressModalLabel">المهام قيد التنفيذ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الأستاذ</th>
                                <th>المستوى</th>
                                <th>المادة</th>
                                <th>الميدان</th>
                                <th>المادة المعرفية</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% set in_progress_entries = [] %}
                            {% for teacher_id, progress in teacher_progress.items() %}
                                {% for entry in progress.recent_entries %}
                                    {% if entry.status == 'in_progress' %}
                                        {% set teacher = teachers|selectattr('id', 'eq', teacher_id)|first %}
                                        {% set in_progress_entries = in_progress_entries + [{'teacher': teacher, 'entry': entry}] %}
                                    {% endif %}
                                {% endfor %}
                            {% endfor %}

                            {% for item in in_progress_entries %}
                            <tr>
                                <td>{{ item.teacher.username }}</td>
                                <td>{{ item.entry.level.name if item.entry.level else 'غير محدد' }}</td>
                                <td>{{ item.entry.subject.name if item.entry.subject else 'غير محدد' }}</td>
                                <td>{{ item.entry.domain.name if item.entry.domain else 'غير محدد' }}</td>
                                <td>{{ item.entry.material.name if item.entry.material else 'غير محدد' }}</td>
                                <td>{{ item.entry.date.strftime('%Y-%m-%d') if item.entry.date else '' }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('teacher_progress_view', teacher_id=item.teacher.id) }}" class="btn btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-info send-notification" data-id="{{ item.teacher.id }}" data-name="{{ item.teacher.username }}">
                                            <i class="fas fa-bell"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}

                            {% if in_progress_entries|length == 0 %}
                            <tr>
                                <td colspan="7" class="text-center">لا توجد مهام قيد التنفيذ</td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Delayed Tasks -->
<div class="modal fade" id="delayedModal" tabindex="-1" aria-labelledby="delayedModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="delayedModalLabel">المهام المتأخرة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الأستاذ</th>
                                <th>المستوى</th>
                                <th>المادة</th>
                                <th>الميدان</th>
                                <th>المادة المعرفية</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>التأخير (أيام)</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>أحمد محمد</td>
                                <td>المستوى الأول</td>
                                <td>الرياضيات</td>
                                <td>الأعداد والحساب</td>
                                <td>الضرب والقسمة</td>
                                <td>2023-05-01</td>
                                <td><span class="badge bg-danger">15</span></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="#" class="btn btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-info send-notification" data-id="1" data-name="أحمد محمد">
                                            <i class="fas fa-bell"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>محمد أحمد</td>
                                <td>المستوى الثالث</td>
                                <td>العلوم</td>
                                <td>المادة وخصائصها</td>
                                <td>التغيرات الفيزيائية</td>
                                <td>2023-05-05</td>
                                <td><span class="badge bg-danger">10</span></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="#" class="btn btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-info send-notification" data-id="3" data-name="محمد أحمد">
                                            <i class="fas fa-bell"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>فاطمة علي</td>
                                <td>المستوى الثاني</td>
                                <td>اللغة العربية</td>
                                <td>القراءة</td>
                                <td>النصوص الشعرية</td>
                                <td>2023-05-08</td>
                                <td><span class="badge bg-danger">7</span></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="#" class="btn btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-info send-notification" data-id="2" data-name="فاطمة علي">
                                            <i class="fas fa-bell"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}
