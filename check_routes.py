from app import app

def check_routes():
    """فحص routes المتاحة في التطبيق"""
    print("🔍 فحص routes المتاحة في التطبيق:")
    
    all_routes = []
    primary_routes = []
    
    for rule in app.url_map.iter_rules():
        route_info = f"{rule.endpoint}: {rule.rule} [{', '.join(rule.methods)}]"
        all_routes.append(route_info)
        
        if 'primary' in rule.endpoint.lower():
            primary_routes.append(route_info)
    
    print(f"📊 إجمالي routes: {len(all_routes)}")
    
    print(f"\n🎯 Primary routes:")
    if primary_routes:
        for route in primary_routes:
            print(f"  ✅ {route}")
    else:
        print("  ❌ لا توجد primary routes")
    
    # البحث عن create_primary_levels تحديداً
    create_primary_routes = [route for route in all_routes if 'create_primary_levels' in route]
    print(f"\n🔍 البحث عن create_primary_levels:")
    if create_primary_routes:
        for route in create_primary_routes:
            print(f"  ✅ {route}")
    else:
        print("  ❌ لم يتم العثور على create_primary_levels route")
    
    # عرض بعض routes المهمة
    print(f"\n📋 بعض routes المهمة:")
    important_keywords = ['admin', 'database', 'manage']
    for keyword in important_keywords:
        matching_routes = [route for route in all_routes if keyword in route.lower()]
        if matching_routes:
            print(f"\n  {keyword.upper()} routes:")
            for route in matching_routes[:5]:  # أول 5 فقط
                print(f"    - {route}")

if __name__ == "__main__":
    check_routes()
