{% extends "base.html" %}

{% block title %}إدارة المفتشين والأساتذة{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- العنوان الرئيسي -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-users-cog me-2"></i>
            إدارة المفتشين والأساتذة
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('admin_dashboard') }}">لوحة التحكم</a></li>
                <li class="breadcrumb-item active" aria-current="page">إدارة المفتشين</li>
            </ol>
        </nav>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي المفتشين
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ all_inspectors|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-tie fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                الأساتذة تحت الإشراف
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% set supervised_count = 0 %}
                                {% for inspector_id, data in inspector_data.items() %}
                                    {% set supervised_count = supervised_count + data.teacher_count %}
                                {% endfor %}
                                {{ supervised_count }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chalkboard-teacher fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                أساتذة غير مرتبطين
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ unassigned_teachers|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-slash fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                متوسط الأساتذة لكل مفتش
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if all_inspectors|length > 0 %}
                                    {{ "%.1f"|format(supervised_count / all_inspectors|length) }}
                                {% else %}
                                    0
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-bar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول المفتشين والأساتذة -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-table me-2"></i>
                المفتشين والأساتذة تحت إشرافهم
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead class="table-dark">
                        <tr>
                            <th style="color: white !important;">المفتش</th>
                            <th style="color: white !important;">البريد الإلكتروني</th>
                            <th style="color: white !important;">عدد الأساتذة</th>
                            <th style="color: white !important;">الأساتذة تحت الإشراف</th>
                            <th style="color: white !important;">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for inspector_id, data in inspector_data.items() %}
                        <tr>
                            <td>
                                <i class="fas fa-user-tie me-2 text-primary"></i>
                                <strong>{{ data.inspector.username }}</strong>
                            </td>
                            <td>{{ data.inspector.email }}</td>
                            <td>
                                <span class="badge bg-primary">{{ data.teacher_count }}</span>
                            </td>
                            <td>
                                {% if data.teachers %}
                                    {% for teacher in data.teachers %}
                                        <span class="badge bg-success me-1 mb-1">
                                            <i class="fas fa-chalkboard-teacher me-1"></i>
                                            {{ teacher.username }}
                                        </span>
                                    {% endfor %}
                                {% else %}
                                    <span class="text-muted">لا يوجد أساتذة</span>
                                {% endif %}
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="viewInspectorDetails({{ inspector_id }})">
                                    <i class="fas fa-eye"></i> عرض التفاصيل
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- جدول الأساتذة غير المرتبطين -->
    {% if unassigned_teachers %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                أساتذة غير مرتبطين بأي مفتش
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead class="table-warning">
                        <tr>
                            <th>اسم الأستاذ</th>
                            <th>البريد الإلكتروني</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for teacher in unassigned_teachers %}
                        <tr>
                            <td>
                                <i class="fas fa-chalkboard-teacher me-2 text-warning"></i>
                                {{ teacher.username }}
                            </td>
                            <td>{{ teacher.email }}</td>
                            <td>{{ teacher.created_at.strftime('%Y-%m-%d') if teacher.created_at else 'غير محدد' }}</td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="assignTeacherToInspector({{ teacher.id }}, '{{ teacher.username }}')">
                                    <i class="fas fa-user-plus"></i> تعيين مفتش
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Modal لتعيين مفتش للأستاذ -->
<div class="modal fade" id="assignInspectorModal" tabindex="-1" aria-labelledby="assignInspectorModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignInspectorModalLabel">تعيين مفتش للأستاذ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('assign_teacher_to_inspector') }}" method="post">
                <div class="modal-body">
                    <input type="hidden" id="modal_teacher_id" name="teacher_id">
                    <div class="mb-3">
                        <label class="form-label">الأستاذ المحدد:</label>
                        <p id="modal_teacher_name" class="fw-bold text-primary"></p>
                    </div>
                    <div class="mb-3">
                        <label for="inspector_id" class="form-label">اختر المفتش:</label>
                        <select class="form-select" id="inspector_id" name="inspector_id">
                            <option value="">إلغاء الإشراف</option>
                            {% for inspector in all_inspectors %}
                            <option value="{{ inspector.id }}">{{ inspector.username }} ({{ inspector.email }})</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تعيين</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function assignTeacherToInspector(teacherId, teacherName) {
    document.getElementById('modal_teacher_id').value = teacherId;
    document.getElementById('modal_teacher_name').textContent = teacherName;
    new bootstrap.Modal(document.getElementById('assignInspectorModal')).show();
}

function viewInspectorDetails(inspectorId) {
    // يمكن إضافة المزيد من التفاصيل هنا
    alert('عرض تفاصيل المفتش - قيد التطوير');
}
</script>
{% endblock %}
