{% extends "base.html" %}

{% block title %}إرسال إشعار{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-paper-plane me-2 text-primary"></i>
            إرسال إشعار
        </h1>
        <a href="{{ url_for('view_notifications') }}" class="btn btn-outline-primary">
            <i class="fas fa-bell me-1"></i>
            عرض الإشعارات
        </a>
    </div>

    <!-- نموذج إرسال الإشعار -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-edit me-2"></i>
                        إنشاء إشعار جديد
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <!-- عنوان الإشعار -->
                        <div class="mb-3">
                            <label for="title" class="form-label">عنوان الإشعار</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>

                        <!-- نص الإشعار -->
                        <div class="mb-3">
                            <label for="message" class="form-label">نص الإشعار</label>
                            <textarea class="form-control" id="message" name="message" rows="4" required></textarea>
                        </div>

                        <!-- نوع الإرسال -->
                        <div class="mb-3">
                            <label for="target_type" class="form-label">إرسال إلى</label>
                            <select class="form-control" id="target_type" name="target_type" required onchange="toggleTargetOptions()">
                                <option value="">اختر نوع الإرسال</option>
                                <option value="all">الجميع</option>
                                <option value="role">دور معين</option>
                                <option value="specific">أشخاص محددين</option>
                            </select>
                        </div>

                        <!-- اختيار الدور -->
                        <div class="mb-3" id="role_selection" style="display: none;">
                            <label for="target_role" class="form-label">اختر الدور</label>
                            <select class="form-control" id="target_role" name="target_role">
                                <option value="">اختر الدور</option>
                                {% if current_user.role == 'admin' %}
                                <option value="inspector">المفتشين</option>
                                <option value="teacher">الأساتذة</option>
                                {% else %}
                                <option value="teacher">الأساتذة</option>
                                {% endif %}
                            </select>
                        </div>

                        <!-- اختيار أشخاص محددين -->
                        <div class="mb-3" id="specific_selection" style="display: none;">
                            <label class="form-label">اختر الأشخاص</label>
                            
                            {% if current_user.role == 'admin' and users_by_role.get('inspectors') %}
                            <div class="mb-3">
                                <h6 class="text-primary">المفتشين</h6>
                                {% for inspector in users_by_role.inspectors %}
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="specific_users" value="{{ inspector.id }}" id="inspector_{{ inspector.id }}">
                                    <label class="form-check-label" for="inspector_{{ inspector.id }}">
                                        {{ inspector.username }} ({{ inspector.email }})
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                            {% endif %}

                            {% if users_by_role.get('teachers') %}
                            <div class="mb-3">
                                <h6 class="text-success">الأساتذة</h6>
                                {% for teacher in users_by_role.teachers %}
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="specific_users" value="{{ teacher.id }}" id="teacher_{{ teacher.id }}">
                                    <label class="form-check-label" for="teacher_{{ teacher.id }}">
                                        {{ teacher.username }} ({{ teacher.email }})
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                            {% endif %}

                            <!-- أزرار تحديد الكل -->
                            <div class="mb-3">
                                <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="selectAll()">
                                    <i class="fas fa-check-square me-1"></i>
                                    تحديد الكل
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="deselectAll()">
                                    <i class="fas fa-square me-1"></i>
                                    إلغاء التحديد
                                </button>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-1"></i>
                                إرسال الإشعار
                            </button>
                            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>
                                العودة
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الإرسال
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary">أنواع الإرسال:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-globe text-info me-2"></i><strong>الجميع:</strong> إرسال لجميع المستخدمين</li>
                            <li><i class="fas fa-users text-success me-2"></i><strong>دور معين:</strong> إرسال لدور محدد</li>
                            <li><i class="fas fa-user text-warning me-2"></i><strong>أشخاص محددين:</strong> إرسال لأشخاص مختارين</li>
                        </ul>
                    </div>

                    {% if current_user.role == 'admin' %}
                    <div class="alert alert-info">
                        <i class="fas fa-crown me-2"></i>
                        <strong>صلاحيات الإدارة:</strong><br>
                        يمكنك إرسال إشعارات لجميع المستخدمين في النظام.
                    </div>
                    {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-user-tie me-2"></i>
                        <strong>صلاحيات المفتش:</strong><br>
                        يمكنك إرسال إشعارات للأساتذة تحت إشرافك فقط.
                    </div>
                    {% endif %}

                    <div class="mt-3">
                        <h6 class="text-success">إحصائيات:</h6>
                        <ul class="list-unstyled">
                            {% if users_by_role.get('inspectors') %}
                            <li><i class="fas fa-user-tie text-primary me-2"></i>المفتشين: {{ users_by_role.inspectors|length }}</li>
                            {% endif %}
                            {% if users_by_role.get('teachers') %}
                            <li><i class="fas fa-chalkboard-teacher text-success me-2"></i>الأساتذة: {{ users_by_role.teachers|length }}</li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleTargetOptions() {
    const targetType = document.getElementById('target_type').value;
    const roleSelection = document.getElementById('role_selection');
    const specificSelection = document.getElementById('specific_selection');
    
    // إخفاء جميع الخيارات
    roleSelection.style.display = 'none';
    specificSelection.style.display = 'none';
    
    // إظهار الخيار المناسب
    if (targetType === 'role') {
        roleSelection.style.display = 'block';
    } else if (targetType === 'specific') {
        specificSelection.style.display = 'block';
    }
}

function selectAll() {
    const checkboxes = document.querySelectorAll('input[name="specific_users"]');
    checkboxes.forEach(checkbox => checkbox.checked = true);
}

function deselectAll() {
    const checkboxes = document.querySelectorAll('input[name="specific_users"]');
    checkboxes.forEach(checkbox => checkbox.checked = false);
}
</script>
{% endblock %}
