from app import app, db
from models_new import User, Role

def test_route_directly():
    """اختبار route مباشرة عبر test client"""
    with app.test_client() as client:
        with app.app_context():
            # البحث عن مستخدم admin
            admin_user = User.query.filter_by(role=Role.ADMIN).first()
            if not admin_user:
                print("❌ لا يوجد مستخدم admin في قاعدة البيانات")
                return
            
            print(f"✅ تم العثور على admin: {admin_user.username}")
            
            # محاولة تسجيل الدخول
            login_response = client.post('/login', data={
                'username': admin_user.username,
                'password': 'admin123'  # كلمة المرور الافتراضية
            }, follow_redirects=True)
            
            print(f"📝 استجابة تسجيل الدخول: {login_response.status_code}")
            
            if login_response.status_code == 200:
                # محاولة الوصول إلى صفحة إدارة قواعد البيانات
                databases_response = client.get('/admin/databases')
                print(f"📊 استجابة صفحة قواعد البيانات: {databases_response.status_code}")
                
                if databases_response.status_code == 200:
                    # محاولة استخدام route إنشاء المستويات
                    create_response = client.post('/admin/create-primary-levels', follow_redirects=True)
                    print(f"🚀 استجابة إنشاء المستويات: {create_response.status_code}")
                    
                    if create_response.status_code == 200:
                        print("✅ route يعمل بشكل صحيح!")
                        
                        # فحص النتيجة
                        from models_new import EducationalLevel, LevelDatabase, LevelDataEntry
                        
                        primary_levels = [
                            'السنة الأولى ابتدائي',
                            'السنة الثانية ابتدائي',
                            'السنة الثالثة ابتدائي',
                            'السنة الرابعة ابتدائي',
                            'السنة الخامسة ابتدائي'
                        ]
                        
                        print("\n📋 فحص النتائج:")
                        for level_name in primary_levels:
                            level = EducationalLevel.query.filter_by(name=level_name).first()
                            if level:
                                level_db = LevelDatabase.query.filter_by(level_id=level.id).first()
                                if level_db:
                                    subjects_count = LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='subject').count()
                                    domains_count = LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='domain').count()
                                    
                                    if subjects_count == 13 and domains_count == 65:
                                        print(f"  ✅ {level_name}: {subjects_count} مادة، {domains_count} ميدان")
                                    else:
                                        print(f"  ❌ {level_name}: {subjects_count} مادة، {domains_count} ميدان (خطأ!)")
                                else:
                                    print(f"  ❌ {level_name}: لا توجد قاعدة بيانات")
                            else:
                                print(f"  ❌ {level_name}: لا يوجد مستوى")
                    else:
                        print(f"❌ route لا يعمل: {create_response.status_code}")
                        print(f"Response data: {create_response.get_data(as_text=True)[:500]}")
                else:
                    print(f"❌ لا يمكن الوصول إلى صفحة قواعد البيانات: {databases_response.status_code}")
            else:
                print(f"❌ فشل تسجيل الدخول: {login_response.status_code}")

def test_with_manual_login():
    """اختبار مع تسجيل دخول يدوي"""
    with app.test_client() as client:
        with app.app_context():
            # محاولة الوصول مباشرة بدون تسجيل دخول
            print("🔍 اختبار الوصول بدون تسجيل دخول:")
            response = client.post('/admin/create-primary-levels')
            print(f"  استجابة: {response.status_code}")
            
            if response.status_code == 302:  # إعادة توجيه
                print("  ✅ تم إعادة التوجيه (طبيعي لعدم تسجيل الدخول)")
            
            # محاولة GET بدلاً من POST
            print("\n🔍 اختبار GET بدلاً من POST:")
            response = client.get('/admin/create-primary-levels')
            print(f"  استجابة: {response.status_code}")

if __name__ == "__main__":
    print("=== اختبار route مباشرة ===")
    test_route_directly()
    
    print("\n" + "="*50)
    print("=== اختبار بدون تسجيل دخول ===")
    test_with_manual_login()
