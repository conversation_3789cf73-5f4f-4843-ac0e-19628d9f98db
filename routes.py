from flask import render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
import pandas as pd
import os
from datetime import datetime

from app import app, db, login_manager
from models_new import User, Role, EducationalLevel, Subject, Domain, KnowledgeMaterial, Competency, Schedule, ProgressEntry, LevelDatabase, LevelDataEntry, AdminInspectorNotification, InspectorTeacherNotification
import json

# User loader for Flask-Login
@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# ملف routes.py - يحتوي فقط على routes الإضافية التي لا توجد في app.py

# Routes إضافية - تم نقل routes الأساسية إلى app.py

# Routes الإشعارات فقط - باقي routes موجودة في app.py

@app.route('/admin/databases/<int:db_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_database(db_id):
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    if request.method == 'POST':
        database.name = request.form.get('name')
        database.file_path = request.form.get('file_path')
        database.is_active = 'is_active' in request.form

        db.session.commit()

        flash('تم تحديث قاعدة البيانات بنجاح', 'success')
        return redirect(url_for('manage_databases'))

    return render_template('edit_database.html', database=database)

@app.route('/admin/databases/<int:db_id>/delete', methods=['POST'])
@login_required
def delete_database(db_id):
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    # Delete all entries first
    LevelDataEntry.query.filter_by(database_id=db_id).delete()

    # Delete database
    db.session.delete(database)
    db.session.commit()

    flash('تم حذف قاعدة البيانات بنجاح', 'success')
    return redirect(url_for('manage_databases'))

@app.route('/admin/databases/<int:db_id>/toggle/<string:action>')
@login_required
def toggle_database(db_id, action):
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    if action == 'activate':
        database.is_active = True
        flash('تم تفعيل قاعدة البيانات بنجاح', 'success')
    elif action == 'deactivate':
        database.is_active = False
        flash('تم تعطيل قاعدة البيانات بنجاح', 'success')

    db.session.commit()
    return redirect(url_for('manage_databases'))

# Database Entries Management
@app.route('/admin/databases/<int:db_id>/entries/add', methods=['POST'])
@login_required
def add_database_entry(db_id):
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    entry_type = request.form.get('entry_type')
    parent_id = request.form.get('parent_id')
    name = request.form.get('name')
    description = request.form.get('description')
    is_active = 'is_active' in request.form

    # Create new entry
    new_entry = LevelDataEntry(
        database_id=db_id,
        entry_type=entry_type,
        parent_id=parent_id if parent_id != '0' else None,
        name=name,
        description=description,
        is_active=is_active
    )

    db.session.add(new_entry)
    db.session.commit()

    flash('تم إضافة العنصر بنجاح', 'success')
    return redirect(url_for('view_database', db_id=db_id))

@app.route('/admin/databases/<int:db_id>/entries/<int:entry_id>/edit', methods=['POST'])
@login_required
def edit_database_entry(db_id, entry_id):
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    entry = LevelDataEntry.query.get_or_404(entry_id)

    entry.name = request.form.get('name')
    entry.description = request.form.get('description')
    entry.is_active = 'is_active' in request.form

    db.session.commit()

    flash('تم تحديث العنصر بنجاح', 'success')
    return redirect(url_for('view_database', db_id=db_id))

@app.route('/admin/databases/<int:db_id>/entries/<int:entry_id>/delete', methods=['POST'])
@login_required
def delete_database_entry(db_id, entry_id):
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    entry = LevelDataEntry.query.get_or_404(entry_id)

    # Delete all child entries recursively
    delete_child_entries(db_id, entry_id)

    # Delete entry
    db.session.delete(entry)
    db.session.commit()

    flash('تم حذف العنصر بنجاح', 'success')
    return redirect(url_for('view_database', db_id=db_id))

# Helper function to delete child entries recursively
def delete_child_entries(db_id, parent_id):
    child_entries = LevelDataEntry.query.filter_by(database_id=db_id, parent_id=parent_id).all()

    for entry in child_entries:
        # Delete children of this entry
        delete_child_entries(db_id, entry.id)

        # Delete this entry
        db.session.delete(entry)

@app.route('/admin/databases/<int:db_id>/import', methods=['POST'])
@login_required
def import_database_data(db_id):
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    if 'file' not in request.files:
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('manage_databases'))

    file = request.files['file']

    if file.filename == '':
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('manage_databases'))

    if not file.filename.endswith('.xlsx'):
        flash('يجب أن يكون الملف بصيغة Excel (.xlsx)', 'danger')
        return redirect(url_for('manage_databases'))

    try:
        # Read Excel file
        df = pd.read_excel(file, sheet_name=None)  # Read all sheets

        # Process each sheet
        for sheet_name, sheet_data in df.items():
            if sheet_name.lower() in ['subjects', 'domains', 'materials', 'competencies']:
                entry_type = sheet_name.lower().rstrip('s')  # Remove trailing 's'

                for _, row in sheet_data.iterrows():
                    # Convert row to dict
                    row_dict = row.to_dict()

                    name = row_dict.get('name', '').strip()
                    if not name:  # تجاهل الصفوف الفارغة
                        continue

                    # التحقق من عدم وجود العنصر مسبقاً لتجنب التكرار
                    existing_entry = LevelDataEntry.query.filter_by(
                        database_id=db_id,
                        entry_type=entry_type,
                        name=name
                    ).first()

                    if existing_entry:
                        continue  # تجاهل العنصر إذا كان موجوداً مسبقاً

                    # Get parent name if applicable
                    parent_id = None
                    if 'parent_name' in row_dict and entry_type != 'subject':
                        parent_name = row_dict['parent_name'].strip()
                        if parent_name:
                            parent_type = {
                                'domain': 'subject',
                                'material': 'domain',
                                'competency': 'material'
                            }[entry_type]

                            parent = LevelDataEntry.query.filter_by(
                                database_id=db_id,
                                entry_type=parent_type,
                                name=parent_name
                            ).first()

                            if parent:
                                parent_id = parent.id

                    # Create new entry
                    new_entry = LevelDataEntry(
                        database_id=db_id,
                        entry_type=entry_type,
                        parent_id=parent_id,
                        name=name,
                        description=row_dict.get('description', '').strip(),
                        is_active=row_dict.get('is_active', True),
                        order_num=row_dict.get('order_num', 0)
                    )

                    db.session.add(new_entry)

        db.session.commit()
        flash('تم استيراد البيانات بنجاح (تم تجاهل العناصر المكررة)', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء استيراد البيانات: {str(e)}', 'danger')

    return redirect(url_for('view_database', db_id=db_id))

@app.route('/admin/databases/<int:db_id>/export')
@login_required
def export_database_data(db_id):
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    # Create a writer for Excel file
    filename = f"database_{database.name}_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
    filepath = os.path.join(app.root_path, 'static', 'exports', filename)
    os.makedirs(os.path.dirname(filepath), exist_ok=True)

    writer = pd.ExcelWriter(filepath, engine='openpyxl')

    # Export subjects
    subjects = LevelDataEntry.query.filter_by(database_id=db_id, entry_type='subject').all()
    subjects_data = [{
        'id': s.id,
        'name': s.name,
        'description': s.description,
        'is_active': s.is_active
    } for s in subjects]

    if subjects_data:
        pd.DataFrame(subjects_data).to_excel(writer, sheet_name='Subjects', index=False)

    # Export domains
    domains = LevelDataEntry.query.filter_by(database_id=db_id, entry_type='domain').all()
    domains_data = []

    for d in domains:
        parent = LevelDataEntry.query.get(d.parent_id) if d.parent_id else None
        domains_data.append({
            'id': d.id,
            'name': d.name,
            'description': d.description,
            'parent_id': d.parent_id,
            'parent_name': parent.name if parent else '',
            'is_active': d.is_active
        })

    if domains_data:
        pd.DataFrame(domains_data).to_excel(writer, sheet_name='Domains', index=False)

    # Export materials
    materials = LevelDataEntry.query.filter_by(database_id=db_id, entry_type='material').all()
    materials_data = []

    for m in materials:
        parent = LevelDataEntry.query.get(m.parent_id) if m.parent_id else None
        materials_data.append({
            'id': m.id,
            'name': m.name,
            'description': m.description,
            'parent_id': m.parent_id,
            'parent_name': parent.name if parent else '',
            'is_active': m.is_active
        })

    if materials_data:
        pd.DataFrame(materials_data).to_excel(writer, sheet_name='Materials', index=False)

    # Export competencies
    competencies = LevelDataEntry.query.filter_by(database_id=db_id, entry_type='competency').all()
    competencies_data = []

    for c in competencies:
        parent = LevelDataEntry.query.get(c.parent_id) if c.parent_id else None
        competencies_data.append({
            'id': c.id,
            'name': c.name,
            'description': c.description,
            'parent_id': c.parent_id,
            'parent_name': parent.name if parent else '',
            'is_active': c.is_active
        })

    if competencies_data:
        pd.DataFrame(competencies_data).to_excel(writer, sheet_name='Competencies', index=False)

    writer.close()

    return redirect(url_for('static', filename=f'exports/{filename}'))

# Data import/export routes (Legacy)
@app.route('/data/export/<string:model_name>')
@login_required
def export_data(model_name):
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    model_map = {
        'levels': EducationalLevel,
        'subjects': Subject,
        'domains': Domain,
        'materials': KnowledgeMaterial,
        'competencies': Competency
    }

    if model_name not in model_map:
        flash('Invalid model name', 'danger')
        return redirect(url_for('admin_dashboard'))

    model = model_map[model_name]
    data = model.query.all()

    # Convert to DataFrame
    df_data = []
    for item in data:
        item_dict = {column.name: getattr(item, column.name) for column in item.__table__.columns}
        df_data.append(item_dict)

    df = pd.DataFrame(df_data)

    # Save to Excel
    filename = f"{model_name}_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
    filepath = os.path.join(app.root_path, 'static', 'exports', filename)
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    df.to_excel(filepath, index=False)

    return redirect(url_for('static', filename=f'exports/{filename}'))

@app.route('/data/import/<string:model_name>', methods=['POST'])
@login_required
def import_data(model_name):
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    model_map = {
        'levels': EducationalLevel,
        'subjects': Subject,
        'domains': Domain,
        'materials': KnowledgeMaterial,
        'competencies': Competency
    }

    if model_name not in model_map:
        flash('Invalid model name', 'danger')
        return redirect(url_for('admin_dashboard'))

    if 'file' not in request.files:
        flash('No file part', 'danger')
        return redirect(url_for('admin_dashboard'))

    file = request.files['file']

    if file.filename == '':
        flash('No selected file', 'danger')
        return redirect(url_for('admin_dashboard'))

    if not file.filename.endswith('.xlsx'):
        flash('File must be an Excel file (.xlsx)', 'danger')
        return redirect(url_for('admin_dashboard'))

    # Read Excel file
    df = pd.read_excel(file)

    # Import data
    model = model_map[model_name]
    for _, row in df.iterrows():
        # Convert row to dict
        row_dict = row.to_dict()

        # Remove id column if present
        if 'id' in row_dict:
            del row_dict['id']

        # Create new instance
        new_instance = model(**row_dict)
        db.session.add(new_instance)

    db.session.commit()

    flash(f'Data imported successfully for {model_name}', 'success')
    return redirect(url_for('admin_dashboard'))

# إنشاء قاعدة بيانات التعليم الابتدائي الثابتة
@app.route('/admin/create-primary-database', methods=['POST'])
@login_required
def create_primary_database():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # البحث عن مستوى التعليم الابتدائي
        primary_level = EducationalLevel.query.filter_by(name='التعليم الابتدائي').first()
        if not primary_level:
            # إنشاء مستوى التعليم الابتدائي إذا لم يكن موجوداً
            primary_level = EducationalLevel(
                name='التعليم الابتدائي',
                is_active=True,
                database_prefix='primary'
            )
            db.session.add(primary_level)
            db.session.flush()

        # البحث عن قاعدة بيانات التعليم الابتدائي
        primary_db = LevelDatabase.query.filter_by(level_id=primary_level.id).first()
        if not primary_db:
            # إنشاء قاعدة بيانات جديدة
            primary_db = LevelDatabase(
                level_id=primary_level.id,
                name='قاعدة بيانات التعليم الابتدائي',
                file_path='data/primary_education.db',
                is_active=True
            )
            db.session.add(primary_db)
            db.session.flush()
        else:
            # حذف جميع البيانات الموجودة في قاعدة البيانات
            LevelDataEntry.query.filter_by(database_id=primary_db.id).delete()

        # تعريف المواد والميادين
        subjects_domains = {
            'اللغة العربية': [
                'فهم المنطوق',
                'التعبير الشفهي',
                'فهم المكتوب (ألعاب قرائية)',
                'التعبير الشفهي ( صيغ )',
                'التعبير الشفهي ( إنتاج)',
                'التعبير الكتابي (أركب)',
                'التعبير الكتابي (إنتاج كتابي)',
                'استكشاف الحرف',
                'كتابة الحرف',
                'إملاء',
                'التراكيب النحوية',
                'الصرف',
                'قراءة اجمالية',
                'قراءة (اداء و فهم)',
                'محفوظات',
                'المشاريع',
                'تطبيقات اللغة',
                'تصحيح التعبير الكتابي',
                'معالجة اللغة العربية',
                'تقويم فصلي في اللغة العربية'
            ],
            'الرياضيات': [
                'الاعداد و الحساب',
                'الفضاء و الهندسة',
                'المقادير و القياس',
                'تنظيم المعطيات',
                'تطبيقات الرياضيات',
                'معالجة الرياضيات',
                'تقويم فصلي في الرياضيات'
            ],
            'التربية الإسلامية': [
                'القرآن والحديث',
                'تهذيب السلوك',
                'مبادئ في العقيدة',
                'مبادئ في السيرة',
                'استعراض النص الشرعي',
                'تقويم فصلي في التربية الإسلامية'
            ],
            'الفرنسية': [
                'حصص الفرنسية'
            ],
            'التربية العلمية': [
                'الإنسان و الصحة',
                'الإنسان و المحيط',
                'المعلمة في الفضاء و الزمن',
                'المادة و علم الأشياء',
                'تقويم فصلي في التربية العلمية'
            ],
            'التربية المدنية': [
                'الحياة الجماعية',
                'الحياة المدنية',
                'الحياة الديمقراطية و المؤسسات',
                'تقويم فصلي في التربية المدنية'
            ],
            'التربية الفنية /التشكيلية': [
                'الرسم و التلوين',
                'فن التصميم',
                'النشيد و الأغنية التربوية',
                'التذوق الموسيقي و الاستماع',
                'القواعد الموسيقية و النظريات',
                'تقويم فصلي التربية الفنية'
            ],
            'التاريخ': [
                'أدوات و مفاهيم مادة التاريخ',
                'التاريخ العام',
                'التاريخ الوطني',
                'تقويم فصلي في التاريخ و الغرافيا'
            ],
            'الجغرافيا': [
                'أدوات و مفاهيم مادة الجغرافيا',
                'السكان و التنمية',
                'السكان و البيئة',
                'تقويم فصلي  في التاريخ و الجغرافيا'
            ],
            'التربية البدنية': [
                'الوضعيات و التنقلات',
                'الحركات القاعدية',
                'الهيكلة و البناء',
                'تقويم فصلي في التربية البدنية'
            ],
            'حفظ القرآن': [
                'استعراض السورة',
                'استعراض الحديث'
            ],
            'الأمازيغية': [
                'ⵜⴰⵎⴰⵣⵉⴳⵀ'
            ],
            'الإنجليزية': [
                'English_lesson'
            ]
        }

        # إضافة المواد والميادين
        for subject_name, domains in subjects_domains.items():
            # إضافة المادة
            subject_entry = LevelDataEntry(
                database_id=primary_db.id,
                entry_type='subject',
                name=subject_name,
                description=f'مادة {subject_name}',
                is_active=True,
                order_num=list(subjects_domains.keys()).index(subject_name) + 1
            )
            db.session.add(subject_entry)
            db.session.flush()  # للحصول على معرف المادة

            # إضافة الميادين للمادة
            for i, domain_name in enumerate(domains):
                domain_entry = LevelDataEntry(
                    database_id=primary_db.id,
                    entry_type='domain',
                    parent_id=subject_entry.id,
                    name=domain_name,
                    description=f'ميدان {domain_name} في مادة {subject_name}',
                    is_active=True,
                    order_num=i + 1
                )
                db.session.add(domain_entry)

        db.session.commit()
        flash('تم إنشاء قاعدة بيانات التعليم الابتدائي بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إنشاء قاعدة البيانات: {str(e)}', 'danger')

    return redirect(url_for('manage_databases'))

# ===== الدوال القديمة محذوفة - نستخدم النظام الموحد في app.py =====



# ===== دالة mark_notification_read محذوفة - نستخدم النسخة المحدثة في app.py =====
