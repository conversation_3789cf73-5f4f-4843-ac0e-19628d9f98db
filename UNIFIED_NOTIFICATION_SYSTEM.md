# توحيد نظام الإشعارات - نظا<PERSON> Ta9affi

## 🎯 المشكلة المحلولة

### **المشكلة الأصلية:**
كان هناك خلط بين عدة صفحات إشعارات منفصلة:
- `http://127.0.0.1:5000/notifications` (النظام الجديد)
- `http://127.0.0.1:5000/inspector/notifications` (النظام القديم)
- `http://127.0.0.1:5000/teacher/notifications` (النظام القديم)
- `http://127.0.0.1:5000/admin/notifications` (النظام القديم)

### **الحل المطبق:**
توحيد جميع الإشعارات في صفحة واحدة موحدة مع الاحتفاظ بنظام الإرسال الجديد المتقدم.

## 🔧 التغييرات المطبقة

### **1. حذف الصفحات القديمة**
```python
# تم حذف هذه الدوال:
# - admin_notifications()
# - send_admin_notification()
# - inspector_notifications()
# - send_inspector_notification()
# - teacher_notifications()
```

### **2. توحيد صفحة عرض الإشعارات**
```python
@app.route('/notifications')
@login_required
def view_notifications():
    # الإشعارات المباشرة (حسب دور المستخدم)
    direct_notifications = []
    
    if current_user.role == Role.INSPECTOR:
        # المفتش يرى الإشعارات من الإدارة
        admin_notifications = AdminInspectorNotification.query.filter(
            AdminInspectorNotification.receiver_id == current_user.id
        ).order_by(AdminInspectorNotification.created_at.desc()).all()
        
        # إضافة نوع للإشعارات
        for notif in admin_notifications:
            notif.notification_type = 'admin_to_inspector'
            direct_notifications.append(notif)
            
    elif current_user.role == Role.TEACHER:
        # الأستاذ يرى الإشعارات من المفتشين
        teacher_notifications = InspectorTeacherNotification.query.filter(
            InspectorTeacherNotification.receiver_id == current_user.id
        ).order_by(InspectorTeacherNotification.created_at.desc()).all()
        
        # إضافة نوع للإشعارات
        for notif in teacher_notifications:
            notif.notification_type = 'inspector_to_teacher'
            direct_notifications.append(notif)
    
    # الإشعارات العامة (للجميع)
    general_notifications = GeneralNotification.query.filter(
        db.or_(
            GeneralNotification.target_type == 'all',
            db.and_(
                GeneralNotification.target_type == 'role',
                GeneralNotification.target_role == current_user.role
            )
        )
    ).order_by(GeneralNotification.created_at.desc()).all()

    # إضافة معلومات القراءة للإشعارات العامة
    for notification in general_notifications:
        read_record = GeneralNotificationRead.query.filter(
            GeneralNotificationRead.notification_id == notification.id,
            GeneralNotificationRead.user_id == current_user.id
        ).first()
        notification.is_read_by_user = bool(read_record)

    return render_template('notifications.html', 
                         direct_notifications=direct_notifications,
                         general_notifications=general_notifications)
```

### **3. تحديث دالة تحديد الإشعار كمقروء**
```python
@app.route('/mark_notification_read/<int:notification_id>/<notification_type>')
@login_required
def mark_notification_read(notification_id, notification_type):
    try:
        if notification_type == 'admin_to_inspector':
            # إشعار من الإدارة للمفتش
            notification = AdminInspectorNotification.query.get_or_404(notification_id)
            if notification.receiver_id == current_user.id:
                notification.is_read = True
                db.session.commit()
        elif notification_type == 'inspector_to_teacher':
            # إشعار من المفتش للأستاذ
            notification = InspectorTeacherNotification.query.get_or_404(notification_id)
            if notification.receiver_id == current_user.id:
                notification.is_read = True
                db.session.commit()
        elif notification_type == 'general':
            # إشعار عام
            notification = GeneralNotification.query.get_or_404(notification_id)
            # ... منطق الإشعارات العامة
        else:
            raise ValueError(f'نوع الإشعار غير مدعوم: {notification_type}')
        
        # دعم AJAX والطلبات العادية
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': True, 'message': 'تم تحديد الإشعار كمقروء'})
        else:
            flash('تم تحديد الإشعار كمقروء', 'success')
    except Exception as e:
        # معالجة الأخطاء
        
    return redirect(url_for('view_notifications'))
```

### **4. تحديث دالة حساب الإشعارات غير المقروءة**
```python
def get_unread_notifications_count_for_user(user_id):
    """
    حساب عدد الإشعارات غير المقروءة للمستخدم (جميع الأنواع)
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return 0
        
        direct_unread = 0
        
        # حساب الإشعارات المباشرة حسب دور المستخدم
        if user.role == Role.INSPECTOR:
            # المفتش: إشعارات من الإدارة
            direct_unread = AdminInspectorNotification.query.filter(
                AdminInspectorNotification.receiver_id == user_id,
                AdminInspectorNotification.is_read == False
            ).count()
        elif user.role == Role.TEACHER:
            # الأستاذ: إشعارات من المفتشين
            direct_unread = InspectorTeacherNotification.query.filter(
                InspectorTeacherNotification.receiver_id == user_id,
                InspectorTeacherNotification.is_read == False
            ).count()
        
        # إشعارات عامة غير مقروءة
        general_notifications = GeneralNotification.query.filter(
            db.or_(
                GeneralNotification.target_type == 'all',
                db.and_(
                    GeneralNotification.target_type == 'role',
                    GeneralNotification.target_role == user.role
                )
            )
        ).all()
        
        general_unread = 0
        for notification in general_notifications:
            is_read = GeneralNotificationRead.query.filter(
                GeneralNotificationRead.notification_id == notification.id,
                GeneralNotificationRead.user_id == user_id
            ).first()
            if not is_read:
                general_unread += 1
        
        return direct_unread + general_unread
        
    except Exception as e:
        print(f"Error calculating unread notifications for user {user_id}: {str(e)}")
        return 0
```

### **5. تحديث القالب الموحد**
```html
<!-- الإشعارات المباشرة -->
{% if direct_notifications %}
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-success">
            <i class="fas fa-user me-2"></i>
            الإشعارات المباشرة
        </h6>
    </div>
    <div class="card-body">
        {% for notification in direct_notifications %}
        <div class="alert {% if notification.is_read %}alert-light{% else %}alert-success{% endif %} d-flex justify-content-between align-items-start">
            <div class="flex-grow-1">
                <div class="d-flex align-items-center mb-2">
                    <h6 class="mb-0 {% if not notification.is_read %}fw-bold{% endif %}">
                        {% if not notification.is_read %}
                        <i class="fas fa-circle text-success me-2" style="font-size: 0.5rem;"></i>
                        {% endif %}
                        {{ notification.title }}
                    </h6>
                    <span class="badge bg-success ms-2">
                        {% if notification.notification_type == 'admin_to_inspector' %}
                            من الإدارة
                        {% elif notification.notification_type == 'inspector_to_teacher' %}
                            من المفتش
                        {% else %}
                            مباشر
                        {% endif %}
                    </span>
                </div>
                <p class="mb-2">{{ notification.message }}</p>
                <small class="text-muted">
                    <i class="fas fa-user me-1"></i>
                    من: {{ notification.sender.username }}
                    <span class="mx-2">|</span>
                    <i class="fas fa-clock me-1"></i>
                    {{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}
                </small>
            </div>
            {% if not notification.is_read %}
            <a href="{{ url_for('mark_notification_read', notification_id=notification.id, notification_type=notification.notification_type) }}" 
               class="btn btn-sm btn-outline-success"
               onclick="markAsReadAndUpdate(event, this)">
                <i class="fas fa-check me-1"></i>
                تحديد كمقروء
            </a>
            {% endif %}
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}
```

## 🎨 الميزات الموحدة

### **1. صفحة واحدة للجميع**
- **URL موحد**: `/notifications` لجميع المستخدمين
- **محتوى ديناميكي**: يتغير حسب دور المستخدم
- **تصميم متسق**: نفس التخطيط والألوان

### **2. أنواع الإشعارات المدعومة**
- **من الإدارة للمفتشين**: `admin_to_inspector`
- **من المفتشين للأساتذة**: `inspector_to_teacher`
- **إشعارات عامة**: `general` (للجميع أو لدور معين)

### **3. شارات توضيحية**
- **"من الإدارة"**: للإشعارات من الإدارة للمفتشين
- **"من المفتش"**: للإشعارات من المفتشين للأساتذة
- **"للجميع"**: للإشعارات العامة للجميع
- **"[اسم الدور]"**: للإشعارات العامة لدور معين

### **4. عداد موحد**
- **حساب شامل**: يشمل جميع أنواع الإشعارات
- **تحديث تلقائي**: يتحدث مع كل تغيير
- **دقة عالية**: حسب دور المستخدم

## 🔄 سير العمل الموحد

### **للمفتش:**
1. **يرى**: الإشعارات من الإدارة + الإشعارات العامة
2. **يرسل**: إشعارات للأساتذة تحت إشرافه
3. **العداد**: إشعارات الإدارة غير المقروءة + الإشعارات العامة

### **للأستاذ:**
1. **يرى**: الإشعارات من المفتشين + الإشعارات العامة
2. **لا يرسل**: إشعارات (يمكن إضافة هذه الميزة لاحقاً)
3. **العداد**: إشعارات المفتشين غير المقروءة + الإشعارات العامة

### **للإدارة:**
1. **يرى**: الإشعارات العامة فقط (أو يمكن إضافة أنواع أخرى)
2. **يرسل**: إشعارات للمفتشين + إشعارات عامة
3. **العداد**: الإشعارات العامة غير المقروءة

## 📊 الفوائد المحققة

### ✅ **بساطة الاستخدام**
- صفحة واحدة بدلاً من عدة صفحات
- واجهة موحدة لجميع المستخدمين
- تنقل أسهل وأوضح

### ✅ **سهولة الصيانة**
- كود أقل وأكثر تنظيماً
- قالب واحد بدلاً من عدة قوالب
- منطق موحد للإشعارات

### ✅ **مرونة أكبر**
- إضافة أنواع إشعارات جديدة بسهولة
- تخصيص المحتوى حسب الدور
- توسيع النظام مستقبلاً

### ✅ **تجربة مستخدم محسنة**
- لا يوجد خلط بين الصفحات
- عداد دقيق وموحد
- تفاعل سلس ومتسق

## 🚀 الحالة النهائية

### ✅ **نظام موحد بالكامل**
- ✅ صفحة واحدة: `/notifications`
- ✅ دالة واحدة: `view_notifications()`
- ✅ قالب واحد: `notifications.html`
- ✅ عداد موحد: `get_unread_notifications_count_for_user()`

### ✅ **دعم جميع الأدوار**
- ✅ الإدارة: إرسال وعرض الإشعارات العامة
- ✅ المفتشين: استقبال من الإدارة + إرسال للأساتذة + الإشعارات العامة
- ✅ الأساتذة: استقبال من المفتشين + الإشعارات العامة

### ✅ **وظائف متقدمة**
- ✅ إرسال للجميع أو لمجموعة أو فردياً
- ✅ تتبع حالة القراءة
- ✅ تحديث فوري للعداد
- ✅ دعم AJAX للتفاعل السريع

### ✅ **تنظيف شامل**
- ✅ حذف الصفحات القديمة
- ✅ حذف القوالب المكررة
- ✅ حذف الدوال المكررة
- ✅ كود نظيف ومنظم

نظام الإشعارات الآن موحد بالكامل ويعمل بكفاءة عالية! 🎯✨

---

**تاريخ التوحيد**: 25 يونيو 2025  
**الحالة**: مكتمل ✅  
**الصفحات الموحدة**: 4 → 1 ✅  
**التحسينات**: شاملة ✅
