{% extends "base.html" %}

{% block title %}الإشعارات{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-bell me-2 text-primary"></i>
            الإشعارات
        </h1>
        <div class="d-flex gap-2">
            {% if current_user.role in ['admin', 'inspector'] %}
            <a href="{{ url_for('send_notification') }}" class="btn btn-primary">
                <i class="fas fa-paper-plane me-1"></i>
                إرسال إشعار
            </a>
            {% endif %}
            <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                العودة
            </a>
        </div>
    </div>

    <!-- الإشعارات العامة -->
    {% if general_notifications %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-info">
                <i class="fas fa-globe me-2"></i>
                الإشعارات العامة
            </h6>
        </div>
        <div class="card-body">
            {% for notification in general_notifications %}
            <div class="alert {% if notification.is_read_by_user %}alert-light{% else %}alert-info{% endif %} d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <div class="d-flex align-items-center mb-2">
                        <h6 class="mb-0 {% if not notification.is_read_by_user %}fw-bold{% endif %}">
                            {% if not notification.is_read_by_user %}
                            <i class="fas fa-circle text-primary me-2" style="font-size: 0.5rem;"></i>
                            {% endif %}
                            {{ notification.title }}
                        </h6>
                        <span class="badge bg-info ms-2">
                            {% if notification.target_type == 'all' %}
                                للجميع
                            {% elif notification.target_type == 'role' %}
                                {{ notification.target_role }}
                            {% endif %}
                        </span>
                    </div>
                    <p class="mb-2">{{ notification.message }}</p>
                    <small class="text-muted">
                        <i class="fas fa-user me-1"></i>
                        من: {{ notification.sender.username }}
                        <span class="mx-2">|</span>
                        <i class="fas fa-clock me-1"></i>
                        {{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}
                    </small>
                </div>
                {% if not notification.is_read_by_user %}
                <a href="{{ url_for('mark_notification_read', notification_id=notification.id, notification_type='general') }}"
                   class="btn btn-sm btn-outline-primary"
                   onclick="markAsReadAndUpdate(event, this)">
                    <i class="fas fa-check me-1"></i>
                    تحديد كمقروء
                </a>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- الإشعارات المباشرة -->
    {% if direct_notifications %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-success">
                <i class="fas fa-user me-2"></i>
                الإشعارات المباشرة
            </h6>
        </div>
        <div class="card-body">
            {% for notification in direct_notifications %}
            <div class="alert {% if notification.is_read %}alert-light{% else %}alert-success{% endif %} d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <div class="d-flex align-items-center mb-2">
                        <h6 class="mb-0 {% if not notification.is_read %}fw-bold{% endif %}">
                            {% if not notification.is_read %}
                            <i class="fas fa-circle text-success me-2" style="font-size: 0.5rem;"></i>
                            {% endif %}
                            {{ notification.title }}
                        </h6>
                        <span class="badge ms-2 {% if notification.notification_type == 'sent_to_teacher' %}bg-info{% else %}bg-success{% endif %}">
                            {% if notification.notification_type == 'admin_to_inspector' %}
                                من الإدارة
                            {% elif notification.notification_type == 'inspector_to_teacher' %}
                                من المفتش
                            {% elif notification.notification_type == 'sent_to_teacher' %}
                                مرسل للأساتذة
                            {% else %}
                                مباشر
                            {% endif %}
                        </span>
                    </div>
                    <p class="mb-2">{{ notification.message }}</p>
                    <small class="text-muted">
                        <i class="fas fa-user me-1"></i>
                        {% if notification.notification_type == 'sent_to_teacher' %}
                            إلى: {{ notification.receiver.username }}
                        {% else %}
                            من: {{ notification.sender.username }}
                        {% endif %}
                        <span class="mx-2">|</span>
                        <i class="fas fa-clock me-1"></i>
                        {{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}
                    </small>
                </div>
                {% if not notification.is_read %}
                <a href="{{ url_for('mark_notification_read', notification_id=notification.id, notification_type=notification.notification_type) }}"
                   class="btn btn-sm btn-outline-success"
                   onclick="markAsReadAndUpdate(event, this)">
                    <i class="fas fa-check me-1"></i>
                    تحديد كمقروء
                </a>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- رسالة عدم وجود إشعارات -->
    {% if not general_notifications and not direct_notifications %}
    <div class="card shadow">
        <div class="card-body text-center py-5">
            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد إشعارات</h5>
            <p class="text-muted">لم تتلق أي إشعارات بعد.</p>
            {% if current_user.role in ['admin', 'inspector'] %}
            <a href="{{ url_for('send_notification') }}" class="btn btn-primary">
                <i class="fas fa-paper-plane me-1"></i>
                إرسال إشعار جديد
            </a>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>

<style>
.alert {
    border-left: 4px solid;
}

.alert-info {
    border-left-color: #17a2b8;
}

.alert-success {
    border-left-color: #28a745;
}

.alert-light {
    border-left-color: #6c757d;
    opacity: 0.8;
}

.badge {
    font-size: 0.75rem;
}

.fas.fa-circle {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}
</style>

<script>
function markAsReadAndUpdate(event, element) {
    event.preventDefault();

    const url = element.href;

    // إرسال طلب تحديد الإشعار كمقروء
    fetch(url, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (response.ok) {
            // إخفاء الزر وتحديث المظهر
            const alertDiv = element.closest('.alert');
            if (alertDiv) {
                alertDiv.classList.remove('alert-info', 'alert-success');
                alertDiv.classList.add('alert-light');

                // إزالة النقطة المضيئة
                const circle = alertDiv.querySelector('.fas.fa-circle');
                if (circle) {
                    circle.remove();
                }

                // إزالة الخط العريض
                const title = alertDiv.querySelector('h6');
                if (title) {
                    title.classList.remove('fw-bold');
                }

                // إخفاء الزر
                element.style.display = 'none';
            }

            // تحديث عداد الإشعارات في القائمة الجانبية
            if (typeof updateNotificationBadge === 'function') {
                updateNotificationBadge();
            }

            // إظهار رسالة نجاح
            const successMessage = document.createElement('div');
            successMessage.className = 'alert alert-success alert-dismissible fade show mt-2';
            successMessage.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                تم تحديد الإشعار كمقروء
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            // إضافة الرسالة في أعلى الصفحة
            const container = document.querySelector('.container-fluid');
            if (container) {
                container.insertBefore(successMessage, container.firstChild);

                // إزالة الرسالة بعد 3 ثوان
                setTimeout(() => {
                    if (successMessage.parentNode) {
                        successMessage.remove();
                    }
                }, 3000);
            }
        } else {
            throw new Error('فشل في تحديد الإشعار كمقروء');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء تحديد الإشعار كمقروء');
    });
}
</script>
{% endblock %}
