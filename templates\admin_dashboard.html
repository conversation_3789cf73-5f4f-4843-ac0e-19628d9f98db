{% extends 'base.html' %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">لوحة تحكم الإدارة</h2>
    </div>
</div>

<div class="row">
    <div class="col-xl-3 col-md-6">
        <div class="card bg-primary text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>المفتشون</div>
                    <div class="h3">{{ inspectors|length }}</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#inspectorsModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-success text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>المستويات التعليمية</div>
                    <div class="h3">5</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#levelsModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-warning text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>المواد الدراسية</div>
                    <div class="h3">12</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#subjectsModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-danger text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>الكفاءات المستهدفة</div>
                    <div class="h3">150+</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#competenciesModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
</div>

<!-- صف إضافي للإحصائيات الجديدة -->
<div class="row">
    <div class="col-xl-3 col-md-6">
        <div class="card bg-secondary text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>حسابات الإدارة</div>
                    <div class="h3">{{ admins|length }}</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#adminsModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-warning text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>حسابات معطلة</div>
                    <div class="h3">{{ pending_users|length }}</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#pendingUsersModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-dark text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>إنشاء حساب جديد</div>
                    <div class="h3"><i class="fas fa-plus animated-icon pulse-icon"></i></div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#createUserModal">إنشاء حساب</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-user-graduate me-1"></i>
                المفتشون
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>اسم المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>عدد الأساتذة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for inspector in inspectors %}
                            <tr>
                                <td>{{ inspector.username }}</td>
                                <td>{{ inspector.email }}</td>
                                <td>{{ inspector.supervised_teachers.count() }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="#" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#viewInspectorModal" data-id="{{ inspector.id }}" data-name="{{ inspector.username }}"><i class="fas fa-eye"></i></a>
                                        <a href="#" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#editInspectorModal" data-id="{{ inspector.id }}" data-name="{{ inspector.username }}" data-email="{{ inspector.email }}"><i class="fas fa-edit"></i></a>
                                        {% if inspector.is_active %}
                                            <a href="{{ url_for('toggle_user_status', user_id=inspector.id) }}" class="btn btn-sm btn-secondary" onclick="return confirm('هل أنت متأكد من تعطيل هذا الحساب؟')"><i class="fas fa-ban"></i></a>
                                        {% else %}
                                            <a href="{{ url_for('toggle_user_status', user_id=inspector.id) }}" class="btn btn-sm btn-success" onclick="return confirm('هل أنت متأكد من تفعيل هذا الحساب؟')"><i class="fas fa-check"></i></a>
                                        {% endif %}
                                        <a href="{{ url_for('delete_user', user_id=inspector.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا الحساب؟ لا يمكن التراجع عن هذه العملية.')"><i class="fas fa-trash"></i></a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-chalkboard-teacher me-1"></i>
                الأساتذة
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>اسم المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>المشرف</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for teacher in teachers %}
                            <tr>
                                <td>{{ teacher.username }}</td>
                                <td>{{ teacher.email }}</td>
                                <td>
                                    {% if teacher.inspectors.first() %}
                                        {{ teacher.inspectors.first().username }}
                                    {% else %}
                                        غير محدد
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="#" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#viewTeacherModal" data-id="{{ teacher.id }}" data-name="{{ teacher.username }}"><i class="fas fa-eye"></i></a>
                                        <a href="#" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#editTeacherModal" data-id="{{ teacher.id }}" data-name="{{ teacher.username }}" data-email="{{ teacher.email }}"><i class="fas fa-edit"></i></a>
                                        {% if teacher.is_active %}
                                            <a href="{{ url_for('toggle_user_status', user_id=teacher.id) }}" class="btn btn-sm btn-secondary" onclick="return confirm('هل أنت متأكد من تعطيل هذا الحساب؟')"><i class="fas fa-ban"></i></a>
                                        {% else %}
                                            <a href="{{ url_for('toggle_user_status', user_id=teacher.id) }}" class="btn btn-sm btn-success" onclick="return confirm('هل أنت متأكد من تفعيل هذا الحساب؟')"><i class="fas fa-check"></i></a>
                                        {% endif %}
                                        <a href="{{ url_for('delete_user', user_id=teacher.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا الحساب؟ لا يمكن التراجع عن هذه العملية.')"><i class="fas fa-trash"></i></a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال المفتشين -->
<div class="modal fade" id="inspectorsModal" tabindex="-1" aria-labelledby="inspectorsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="inspectorsModalLabel">تفاصيل المفتشين</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>اسم المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>عدد الأساتذة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for inspector in inspectors %}
                            <tr>
                                <td>{{ inspector.username }}</td>
                                <td>{{ inspector.email }}</td>
                                <td>{{ inspector.supervised_teachers.count() }}</td>
                                <td>
                                    <a href="{{ url_for('dashboard') }}" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i> عرض</a>
                                    <a href="#" class="btn btn-sm btn-warning"><i class="fas fa-edit"></i> تعديل</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال المستويات التعليمية -->
<div class="modal fade" id="levelsModal" tabindex="-1" aria-labelledby="levelsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="levelsModalLabel">تفاصيل المستويات التعليمية</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>المستوى</th>
                                <th>عدد المواد</th>
                                <th>عدد الميادين</th>
                                <th>عدد الكفاءات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>السنة الأولى ابتدائي</td>
                                <td>13</td>
                                <td>25</td>
                                <td>50</td>
                                <td>
                                    <a href="{{ url_for('manage_databases') }}" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                            <tr>
                                <td>السنة الثانية ابتدائي</td>
                                <td>13</td>
                                <td>25</td>
                                <td>50</td>
                                <td>
                                    <a href="{{ url_for('manage_databases') }}" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                            <tr>
                                <td>السنة الثالثة ابتدائي</td>
                                <td>13</td>
                                <td>25</td>
                                <td>50</td>
                                <td>
                                    <a href="{{ url_for('manage_databases') }}" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                            <tr>
                                <td>السنة الرابعة ابتدائي</td>
                                <td>13</td>
                                <td>25</td>
                                <td>50</td>
                                <td>
                                    <a href="{{ url_for('manage_databases') }}" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                            <tr>
                                <td>السنة الخامسة ابتدائي</td>
                                <td>13</td>
                                <td>25</td>
                                <td>50</td>
                                <td>
                                    <a href="{{ url_for('manage_databases') }}" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال المواد الدراسية -->
<div class="modal fade" id="subjectsModal" tabindex="-1" aria-labelledby="subjectsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="subjectsModalLabel">تفاصيل المواد الدراسية</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>المادة</th>
                                <th>المستوى</th>
                                <th>عدد الميادين</th>
                                <th>عدد الكفاءات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>اللغة العربية</td>
                                <td>جميع المستويات</td>
                                <td>5</td>
                                <td>25</td>
                                <td>
                                    <a href="{{ url_for('manage_databases') }}" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                            <tr>
                                <td>الرياضيات</td>
                                <td>جميع المستويات</td>
                                <td>4</td>
                                <td>20</td>
                                <td>
                                    <a href="{{ url_for('manage_databases') }}" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                            <tr>
                                <td>التربية الإسلامية</td>
                                <td>جميع المستويات</td>
                                <td>3</td>
                                <td>15</td>
                                <td>
                                    <a href="{{ url_for('manage_databases') }}" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                            <tr>
                                <td>التربية العلمية والتكنولوجية</td>
                                <td>جميع المستويات</td>
                                <td>4</td>
                                <td>20</td>
                                <td>
                                    <a href="{{ url_for('manage_databases') }}" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال الكفاءات المستهدفة -->
<div class="modal fade" id="competenciesModal" tabindex="-1" aria-labelledby="competenciesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="competenciesModalLabel">تفاصيل الكفاءات المستهدفة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> يمكنك عرض الكفاءات المستهدفة من خلال صفحة قواعد البيانات.
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('manage_databases') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-database"></i> الذهاب إلى قواعد البيانات
                    </a>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال عرض المفتش -->
<div class="modal fade" id="viewInspectorModal" tabindex="-1" aria-labelledby="viewInspectorModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="viewInspectorModalLabel">تفاصيل المفتش</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label fw-bold">اسم المستخدم:</label>
                    <p id="viewInspectorUsername"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">البريد الإلكتروني:</label>
                    <p id="viewInspectorEmail"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">عدد الأساتذة تحت الإشراف:</label>
                    <p id="viewInspectorTeachersCount"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">الحالة:</label>
                    <p id="viewInspectorStatus"></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال تعديل المفتش -->
<div class="modal fade" id="editInspectorModal" tabindex="-1" aria-labelledby="editInspectorModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="editInspectorModalLabel">تعديل بيانات المفتش</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editInspectorForm" action="{{ url_for('edit_user') }}" method="post">
                <div class="modal-body">
                    <input type="hidden" id="editInspectorId" name="user_id">
                    <div class="mb-3">
                        <label for="editInspectorUsername" class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="editInspectorUsername" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="editInspectorEmail" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="editInspectorEmail" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="editInspectorPassword" class="form-label">كلمة المرور (اتركها فارغة للاحتفاظ بنفس كلمة المرور)</label>
                        <input type="password" class="form-control" id="editInspectorPassword" name="password">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- مودال عرض الأستاذ -->
<div class="modal fade" id="viewTeacherModal" tabindex="-1" aria-labelledby="viewTeacherModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="viewTeacherModalLabel">تفاصيل الأستاذ</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label fw-bold">اسم المستخدم:</label>
                    <p id="viewTeacherUsername"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">البريد الإلكتروني:</label>
                    <p id="viewTeacherEmail"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">المشرف:</label>
                    <p id="viewTeacherInspector"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">الحالة:</label>
                    <p id="viewTeacherStatus"></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال تعديل الأستاذ -->
<div class="modal fade" id="editTeacherModal" tabindex="-1" aria-labelledby="editTeacherModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="editTeacherModalLabel">تعديل بيانات الأستاذ</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editTeacherForm" action="{{ url_for('edit_user') }}" method="post">
                <div class="modal-body">
                    <input type="hidden" id="editTeacherId" name="user_id">
                    <div class="mb-3">
                        <label for="editTeacherUsername" class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="editTeacherUsername" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="editTeacherEmail" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="editTeacherEmail" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="editTeacherPassword" class="form-label">كلمة المرور (اتركها فارغة للاحتفاظ بنفس كلمة المرور)</label>
                        <input type="password" class="form-control" id="editTeacherPassword" name="password">
                    </div>
                    <div class="mb-3">
                        <label for="editTeacherInspector" class="form-label">المشرف</label>
                        <select class="form-select" id="editTeacherInspector" name="inspector_id">
                            <option value="">اختر المشرف</option>
                            {% for inspector in inspectors %}
                            <option value="{{ inspector.id }}">{{ inspector.username }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    // كود JavaScript للتعامل مع المودالات
    document.addEventListener('DOMContentLoaded', function() {
        // التعامل مع مودال المفتشين
        const inspectorsModal = document.getElementById('inspectorsModal');
        if (inspectorsModal) {
            inspectorsModal.addEventListener('show.bs.modal', function(event) {
                // يمكن إضافة كود لتحديث البيانات في المودال قبل عرضه
            });
        }

        // التعامل مع مودال المستويات
        const levelsModal = document.getElementById('levelsModal');
        if (levelsModal) {
            levelsModal.addEventListener('show.bs.modal', function(event) {
                // يمكن إضافة كود لتحديث البيانات في المودال قبل عرضه
            });
        }

        // التعامل مع مودال المواد
        const subjectsModal = document.getElementById('subjectsModal');
        if (subjectsModal) {
            subjectsModal.addEventListener('show.bs.modal', function(event) {
                // يمكن إضافة كود لتحديث البيانات في المودال قبل عرضه
            });
        }

        // التعامل مع مودال الكفاءات
        const competenciesModal = document.getElementById('competenciesModal');
        if (competenciesModal) {
            competenciesModal.addEventListener('show.bs.modal', function(event) {
                // يمكن إضافة كود لتحديث البيانات في المودال قبل عرضه
            });
        }

        // التعامل مع مودال عرض المفتش
        const viewInspectorModal = document.getElementById('viewInspectorModal');
        if (viewInspectorModal) {
            viewInspectorModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const id = button.getAttribute('data-id');
                const name = button.getAttribute('data-name');
                const email = button.closest('tr').querySelector('td:nth-child(2)').textContent;
                const teachersCount = button.closest('tr').querySelector('td:nth-child(3)').textContent;
                const isActive = button.closest('tr').querySelector('.btn-secondary') ? true : false;

                document.getElementById('viewInspectorUsername').textContent = name;
                document.getElementById('viewInspectorEmail').textContent = email;
                document.getElementById('viewInspectorTeachersCount').textContent = teachersCount;
                document.getElementById('viewInspectorStatus').textContent = isActive ? 'مفعل' : 'معطل';
                document.getElementById('viewInspectorStatus').className = isActive ? 'badge bg-success' : 'badge bg-secondary';
            });
        }

        // التعامل مع مودال تعديل المفتش
        const editInspectorModal = document.getElementById('editInspectorModal');
        if (editInspectorModal) {
            editInspectorModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const id = button.getAttribute('data-id');
                const name = button.getAttribute('data-name');
                const email = button.getAttribute('data-email');

                document.getElementById('editInspectorId').value = id;
                document.getElementById('editInspectorUsername').value = name;
                document.getElementById('editInspectorEmail').value = email;
                document.getElementById('editInspectorPassword').value = '';
            });
        }

        // التعامل مع مودال عرض الأستاذ
        const viewTeacherModal = document.getElementById('viewTeacherModal');
        if (viewTeacherModal) {
            viewTeacherModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const id = button.getAttribute('data-id');
                const name = button.getAttribute('data-name');
                const email = button.closest('tr').querySelector('td:nth-child(2)').textContent;
                const inspector = button.closest('tr').querySelector('td:nth-child(3)').textContent;
                const isActive = button.closest('tr').querySelector('.btn-secondary') ? true : false;

                document.getElementById('viewTeacherUsername').textContent = name;
                document.getElementById('viewTeacherEmail').textContent = email;
                document.getElementById('viewTeacherInspector').textContent = inspector;
                document.getElementById('viewTeacherStatus').textContent = isActive ? 'مفعل' : 'معطل';
                document.getElementById('viewTeacherStatus').className = isActive ? 'badge bg-success' : 'badge bg-secondary';
            });
        }

        // التعامل مع مودال تعديل الأستاذ
        const editTeacherModal = document.getElementById('editTeacherModal');
        if (editTeacherModal) {
            editTeacherModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const id = button.getAttribute('data-id');
                const name = button.getAttribute('data-name');
                const email = button.getAttribute('data-email');
                const inspectorRow = button.closest('tr').querySelector('td:nth-child(3)').textContent.trim();

                document.getElementById('editTeacherId').value = id;
                document.getElementById('editTeacherUsername').value = name;
                document.getElementById('editTeacherEmail').value = email;
                document.getElementById('editTeacherPassword').value = '';

                // محاولة تحديد المشرف الحالي في القائمة
                const inspectorSelect = document.getElementById('editTeacherInspector');
                if (inspectorSelect) {
                    const options = inspectorSelect.options;
                    for (let i = 0; i < options.length; i++) {
                        if (options[i].text === inspectorRow) {
                            inspectorSelect.selectedIndex = i;
                            break;
                        }
                    }
                }
            });
        }
    });
</script>
{% endblock %}

<!-- مودال إنشاء مستخدم جديد -->
<div class="modal fade" id="createUserModal" tabindex="-1" aria-labelledby="createUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="createUserModalLabel">
                    <i class="fas fa-user-plus animated-icon me-2"></i>
                    إنشاء حساب جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('admin_create_user') }}" method="post">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="createUsername" name="username" required>
                                <label for="createUsername">
                                    <i class="fas fa-user me-1"></i>
                                    اسم المستخدم
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="email" class="form-control" id="createEmail" name="email" required>
                                <label for="createEmail">
                                    <i class="fas fa-envelope me-1"></i>
                                    البريد الإلكتروني
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="password" class="form-control" id="createPassword" name="password" required>
                                <label for="createPassword">
                                    <i class="fas fa-lock me-1"></i>
                                    كلمة المرور
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="createRole" name="role" required>
                                    <option value="" selected disabled>اختر الدور</option>
                                    <option value="teacher">أستاذ</option>
                                    <option value="inspector">مفتش</option>
                                    <option value="admin">إدارة</option>
                                </select>
                                <label for="createRole">
                                    <i class="fas fa-user-tag me-1"></i>
                                    الدور
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> سيتم تفعيل الحساب تلقائياً عند الإنشاء من قبل الإدارة.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>
                        إنشاء الحساب
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- مودال عرض حسابات الإدارة -->
<div class="modal fade" id="adminsModal" tabindex="-1" aria-labelledby="adminsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-secondary text-white">
                <h5 class="modal-title" id="adminsModalLabel">
                    <i class="fas fa-user-shield animated-icon me-2"></i>
                    حسابات الإدارة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>اسم المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>تاريخ التسجيل</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for admin in admins %}
                            <tr>
                                <td>{{ admin.username }}</td>
                                <td>{{ admin.email }}</td>
                                <td>{{ admin.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if admin.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-danger">معطل</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال عرض الحسابات المعطلة -->
<div class="modal fade" id="pendingUsersModal" tabindex="-1" aria-labelledby="pendingUsersModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="pendingUsersModalLabel">
                    <i class="fas fa-clock animated-icon me-2"></i>
                    الحسابات المعطلة (في انتظار التفعيل)
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                {% if pending_users %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>اسم المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الدور</th>
                                <th>تاريخ التسجيل</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in pending_users %}
                            <tr>
                                <td>{{ user.username }}</td>
                                <td>{{ user.email }}</td>
                                <td>
                                    {% if user.role == 'admin' %}
                                        <span class="badge bg-danger">إدارة</span>
                                    {% elif user.role == 'inspector' %}
                                        <span class="badge bg-warning">مفتش</span>
                                    {% else %}
                                        <span class="badge bg-success">أستاذ</span>
                                    {% endif %}
                                </td>
                                <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <a href="{{ url_for('toggle_user_status', user_id=user.id) }}"
                                       class="btn btn-sm btn-success"
                                       onclick="return confirm('هل أنت متأكد من تفعيل هذا الحساب؟')">
                                        <i class="fas fa-check"></i> تفعيل
                                    </a>
                                    <a href="{{ url_for('delete_user', user_id=user.id) }}"
                                       class="btn btn-sm btn-danger"
                                       onclick="return confirm('هل أنت متأكد من حذف هذا الحساب؟')">
                                        <i class="fas fa-trash"></i> حذف
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5>لا توجد حسابات معطلة</h5>
                    <p class="text-muted">جميع الحسابات مفعلة حالياً</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% endblock %}
