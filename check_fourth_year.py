from app import app, db
from models_new import EducationalLevel, LevelDatabase, LevelDataEntry

def check_fourth_year_database():
    """فحص قاعدة بيانات السنة الرابعة لرؤية المواد المكررة"""
    with app.app_context():
        # البحث عن السنة الرابعة ابتدائي
        level = EducationalLevel.query.filter_by(name='السنة الرابعة ابتدائي').first()
        if not level:
            print("لا توجد السنة الرابعة ابتدائي")
            return
            
        level_db = LevelDatabase.query.filter_by(level_id=level.id).first()
        if not level_db:
            print("لا توجد قاعدة بيانات للسنة الرابعة ابتدائي")
            return
        
        print(f"قاعدة البيانات: {level_db.name}")
        print(f"معرف قاعدة البيانات: {level_db.id}")
        
        # عد جميع العناصر
        total_count = LevelDataEntry.query.filter_by(database_id=level_db.id).count()
        subjects_count = LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='subject').count()
        domains_count = LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='domain').count()
        materials_count = LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='material').count()
        competencies_count = LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='competency').count()
        
        print(f"إجمالي العناصر: {total_count}")
        print(f"المواد: {subjects_count}")
        print(f"الميادين: {domains_count}")
        print(f"المواد المعرفية: {materials_count}")
        print(f"الكفاءات: {competencies_count}")
        
        # عرض جميع المواد
        print("\n📚 المواد الموجودة:")
        subjects = LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='subject').all()
        subject_names = {}
        for subject in subjects:
            if subject.name in subject_names:
                subject_names[subject.name] += 1
            else:
                subject_names[subject.name] = 1
        
        for name, count in subject_names.items():
            if count > 1:
                print(f"  ❌ {name} (مكرر {count} مرات)")
            else:
                print(f"  ✅ {name}")
        
        # عرض الميادين المكررة
        print("\n🏛️ الميادين المكررة:")
        domains = LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='domain').all()
        domain_names = {}
        for domain in domains:
            if domain.name in domain_names:
                domain_names[domain.name] += 1
            else:
                domain_names[domain.name] = 1
        
        duplicated_domains = {name: count for name, count in domain_names.items() if count > 1}
        if duplicated_domains:
            for name, count in duplicated_domains.items():
                print(f"  ❌ {name} (مكرر {count} مرات)")
        else:
            print("  ✅ لا توجد ميادين مكررة")
        
        # عرض المواد غير المطلوبة
        print("\n🔍 المواد غير المطلوبة:")
        expected_subjects = [
            'اللغة العربية', 'الرياضيات', 'التربية الإسلامية', 'الفرنسية',
            'التربية العلمية', 'التربية المدنية', 'التربية الفنية /التشكيلية',
            'التاريخ', 'الجغرافيا', 'التربية البدنية', 'حفظ القرآن',
            'الأمازيغية', 'الإنجليزية'
        ]
        
        for name in subject_names.keys():
            if name not in expected_subjects:
                print(f"  ⚠️ {name} (غير مطلوب)")

def force_recreate_fourth_year():
    """إعادة إنشاء قاعدة بيانات السنة الرابعة بالقوة"""
    with app.app_context():
        # البحث عن السنة الرابعة ابتدائي
        level = EducationalLevel.query.filter_by(name='السنة الرابعة ابتدائي').first()
        if not level:
            print("لا توجد السنة الرابعة ابتدائي")
            return
            
        level_db = LevelDatabase.query.filter_by(level_id=level.id).first()
        if not level_db:
            print("لا توجد قاعدة بيانات للسنة الرابعة ابتدائي")
            return
        
        print(f"حذف جميع البيانات من: {level_db.name}")
        
        # حذف جميع البيانات
        deleted_count = LevelDataEntry.query.filter_by(database_id=level_db.id).count()
        LevelDataEntry.query.filter_by(database_id=level_db.id).delete()
        print(f"تم حذف {deleted_count} عنصر")
        
        # إعادة إنشاء البيانات الصحيحة
        from app import add_subjects_to_level
        print("إعادة إنشاء المواد والميادين الصحيحة...")
        add_subjects_to_level(level_db.id)
        
        db.session.commit()
        
        # التحقق من النتيجة
        subjects_count = LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='subject').count()
        domains_count = LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='domain').count()
        
        print(f"✅ تم الانتهاء: {subjects_count} مادة، {domains_count} ميدان")

if __name__ == "__main__":
    print("=== فحص قاعدة بيانات السنة الرابعة ===")
    check_fourth_year_database()
    
    print("\n" + "="*50)
    print("=== إعادة إنشاء قاعدة بيانات السنة الرابعة ===")
    force_recreate_fourth_year()
    
    print("\n" + "="*50)
    print("=== فحص النتيجة النهائية ===")
    check_fourth_year_database()
